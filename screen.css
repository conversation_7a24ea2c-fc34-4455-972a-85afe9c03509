@charset "UTF-8";
/***************
 * Szabvány:    3.0
 * Név:         screen.css
 * Projekt:     Tölgyfa Panzió
 * Leírás:      
 * Keletkezés:  2019.12.24
 * Fejlesztő:   <PERSON><PERSON><PERSON><PERSON>
 * 182/78 940/560 responsive kép
 ***************/
/* alap mező magasság */
/* form mező font méret */
/* margó */
/* legkisebb mező szélesség */
/* lépésköz mező szélesség méreteknél 320px-ig */
:root{
  --colorPrimary1: #9ff2c7;
  --colorPrimary2: #000000;
  --colorPrimary3: #469621;
  --colorPrimary4: #000000;
  --colorSecondary1: #000000;
  --colorSecondary2: #ffffff;
  --colorSecondary3: #000000;
  --colorSecondary4: #000000;
  --colorTertiary1: #7f0000;
  --colorTertiary2: #000000;
  --colorTertiary3: #000000;
  --colorTertiary4: #000000;
  --colorDanger: #ff0000;
  --colorWarning: #ffc107;
  --colorSuccess: #666666;
  --colorInfo: #17a2b8;

  --icon-facebook: '\e000';
  --icon-instagram: '\e001';
  --icon-twitter: '\e002';
  --icon-arrows-alt: '\e003';
  --icon-money: '\e004';
  --icon-user: '\e005';
  --icon-bed: '\e006';
  --icon-chevron-circle-left: '\e007';
  --icon-chevron-circle-right: '\e008';
  --icon-wifi: '\e009';
  --icon-terrace: '\e00a';
  --icon-animal-allowed: '\e00b';
  --icon-credit-cards: '\e00c';
  --icon-shower: '\e00d';
  --icon-toilet-paper: '\e00e';
  --icon-air-conditioner: '\e00f';
  --icon-tv: '\e010';
  --icon-smoking-no: '\e011';
  --icon-check: '\e012';
  --icon-chevron-down: '\e013';
  --icon-grill: '\e014';
}

/***************
 * Szabvány:    3.0
 * Név:         _spritzer.scss
 * Projekt:     front-end komponens eszköztár / front-end component library
 * Leírás:      SCSS eszköztár / SCSS library
 * Keletkezés:  2018.08.01
 * Fejlesztő:   Tánczos Róbert
 * Verzió:      2.2
 *
 *    18.08.01  2.00 spritzer külön csomagjainak egységesítése _form + _library + _help + _reset
 *    19.07.28  2.10 csomagok importba elemenként, spritzer csak leírás és rövid részek. új csomag: dashboard
 *    20.02.09  2.20 csomagok importba elemenként, spritzer csak leírás és rövid részek. új csomag: gdpr
 ***************/
/* reset */
/* form-text */
/* add-on, add-to */
/* form-area */
/* form-select */
/* timebox */
/* click ikon */
/* radio és checkbox */
/* filebox */
/* filebox drag & drop + move */
/* lapozo */
/* gomb */
/* icon */
/* buborek */
/* füles menü */
/* Tooltip */
/* Táblázat
 * table-color
 * table-border-color
 * $table-head-color
 * table-head-bg-color
 * table-foot-color
 * table-foot-bg-color
 */
/*
 * dashboard-color
 * dashboard-bg-color
 * dashboard-hover-color
 * dashboard-hover-bg-color
 * dashboard-sec-color
 * dashboard-sec-bg-color
 * dashboard-sec-hover-color
 * dashboard-active-color
 * dashboard-active-bg-color
 * dashboard-active-border-color
 */
/*
 * gdpr-color
 * gdpr-bg-color
 * gdpr-border-color
 * gdpr-link-color
 * gdpr-link-bg-color
 * gdpr-hover-color
 */
.vat {
  height: 2.25em;
  line-height: 2.25em;
}

.vac {
  display: flex;
  align-content: center;
}

.tac {
  text-align: center;
}

.tar {
  text-align: right;
}

.fmve > li {
  float: left;
}
.fmve:after {
  content: "";
  display: block;
  clear: both;
}

.mart {
  margin-top: 4px;
}

.marb {
  margin-bottom: 4px;
}

.marlr {
  margin-left: 4px;
  margin-right: 4px;
}

.padlil > li, .padl {
  padding-left: 4px;
}

.padlir > li, .padr {
  padding-right: 4px;
}

.padlilr > li, .padlr {
  padding-left: 4px;
  padding-right: 4px;
}

.padlit > li,
.padt {
  padding-top: 4px;
}

.padb,
.padlib > li {
  padding-bottom: 4px;
}

.padtb {
  padding-top: 4px;
  padding-bottom: 4px;
}

.gridw1 {
  width: 20px;
}

.gridw2 {
  width: 40px;
}

.gridw3 {
  width: 80px;
}

.gridw4 {
  width: 120px;
}

.gridw5 {
  width: 160px;
}

.gridw6 {
  width: 200px;
}

.gridw7 {
  width: 240px;
}

.gridw8 {
  width: 280px;
}

.gridw9 {
  width: 320px;
}

.gridw2f {
  width: 60px;
}

.gridw3f {
  width: 100px;
}

.gridw4f {
  width: 140px;
}

.gridw5f {
  width: 180px;
}

.gridw6f {
  width: 220px;
}

.gridw7f {
  width: 260px;
}

.gridw13 {
  width: 480px;
}

.gridw18 {
  width: 640px;
}

.gridws1 {
  max-width: 10%;
}

.gridws2 {
  max-width: 20%;
}

.gridws25 {
  max-width: 25%;
}

.gridws3 {
  max-width: 30%;
}

.gridws33 {
  max-width: 33.33%;
  width: 33.33%;
}

.gridws4 {
  max-width: 40%;
}

.gridws5 {
  max-width: 50%;
  width: 50%;
}

.gridws7 {
  max-width: 70%;
  width: 70%;
}

.gridws66 {
  max-width: 66.66%;
  width: 66.66%;
}

.gridws75 {
  max-width: 75%;
  width: 75%;
}

.gridws8 {
  max-width: 80%;
  width: 80%;
}

.gridws0 {
  max-width: 100%;
  width: 100%;
}

@font-face {
  font-family: "tolgyfapanzio";
  src:url("items/tolgyfapanzio.eot");
  src:url("items/tolgyfapanzio.eot?#iefix") format("embedded-opentype"),
      url("items/tolgyfapanzio.woff") format("woff"),
      url("items/tolgyfapanzio.ttf") format("truetype"),
      url("items/tolgyfapanzio.svg#tolgyfapanzio") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: fallback;
}
::before, ::after,
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  padding: 0;
  border: 0;
}

img, fieldset {
  border: none;
}

a, object,
input:focus,
textarea:focus,
button:focus {
  outline: none !important;
}

li {
  list-style: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

h1, h2, h3, h4, h5, h6,
html {
  font-size: 100%;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: bold;
}

button,
input[type=button],
input[type=reset],
input[type=submit],
input[type=checkbox],
input[type=radio],
select {
  cursor: pointer;
}

button[disabled],
input[disabled] {
  cursor: default;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
nav,
section,
summary,
video,
img,
object,
iframe {
  display: block;
}

@-moz-viewport {
  width: device-width;
  scale: 1;
}
@-ms-viewport {
  width: device-width;
  scale: 1;
}
@-o-viewport {
  width: device-width;
  scale: 1;
}
@-webkit-viewport {
  width: device-width;
  scale: 1;
}
@viewport {
  width: device-width;
  scale: 1;
}
html, body {
  height: 100%;
  color: var(--colorPrimary2);
  background-color: var(--colorSecondary2);
}

body {
  font-size: 16px;
  font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji;
}

a {
  text-decoration: none;
  cursor: pointer;
  color: var(--colorTertiary1);
}
a:hover {
  text-decoration: underline;
}

h1, h2, h3 {
  color: var(--colorPrimary3);
}

h1 {
  width: 100%;
  font-size: 2em;
  padding: 2em 0;
}

main,
.wrapper {
  position: relative;
  width: 100%;
}

header {
  display: flex;
  justify-content: space-between;
  flex-flow: row wrap;
  -webkit-flex-flow: row wrap;
  align-items: center;
  position: relative;
  width: 100%;
  /*min-height: 50px;*/
  background-color: inherit;
}
header .header-nav {
  display: flex;
  justify-content: space-between;
  flex-flow: row wrap;
  -webkit-flex-flow: row wrap;
  align-items: center;
  padding: 5px;
  margin: 0;
  list-style: none;
}
header .header-nav .logo {
  background: url(/items/logo.png) no-repeat;
  background-size: contain;
  width: 100%;
  /*height: 60px;*/
  text-align: right;
  font-size: 22px;
  font-weight: 600;
}
header .header-nav .logo > a > b {
  font-size: 22px;
}
header .header-nav .logo > a:hover {
  text-decoration: none;
  color: var(--colorPrimary3);
}
header .header-links > li {
  display: inline-block;
}
header .header-links > li > a {
  position: relative;
  display: block;
  /*min-height: 50px;*/
  padding: 15px 10px;
  font-size: 14px;
  font-weight: 600;
}
header .header-links > li > a:hover {
  text-decoration: none;
  color: var(--colorPrimary3);
}
header .header-links > li.navbar > a {
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: uppercase;
}

footer {
  display: flex;
  justify-content: space-between;
  flex-flow: row wrap;
  -webkit-flex-flow: row wrap;
  align-items: center;
  gap: 10px;
  margin-top: 30px;
  padding: 10px 8vw;
  background-color: var(--colorPrimary3);
  color: var(--colorSecondary2);
}
footer > div {
  width: 100%;
  font-size: 0.9em;
}

.content {
  position: relative !important;
  min-height: 576px;
  margin: 0;
}

#koszon .content .container,
#home .content .container {
  justify-content: space-between;
}
#koszon .content .container .leiras,
#home .content .container .leiras {
  margin-top: 3.75em;
  padding: 0 8vw 3.85em;
}
#koszon .content .container .leiras h1,
#home .content .container .leiras h1 {
  padding-top: 0;
}
#koszon .foglalas,
#home .foglalas {
  position: relative;
  margin: 0 auto;
  background-color: var(--colorPrimary3);
  color: var(--colorSecondary2);
}
#koszon .foglalas ul,
#home .foglalas ul {
  display: flex;
  justify-content: space-around;
  flex-flow: row wrap;
  -webkit-flex-flow: row wrap;
  gap: 10px;
  align-items: center;
  padding: 42px 10px;
}
#koszon .foglalas ul li input[type=text],
#home .foglalas ul li input[type=text] {
  height: 50px;
}
#koszon .foglalas ul li:first-child,
#home .foglalas ul li:first-child {
  font-size: 32px;
  max-width: 320px;
  text-align: center;
}
#koszon .foglalas ul li:nth-child(2),
#home .foglalas ul li:nth-child(2) {
  display: flex;
  gap: 10px;
}
#koszon .foglalas ul li div,
#home .foglalas ul li div {
  max-width: 155px;
  width: 50%;
}

#galeria .content .container {
  margin: 0 auto;
  justify-content: center;
}
#galeria .content .container h1 {
  max-width: 960px;
  min-width: 320px;
  padding: 0 20px;
  margin-bottom: 2em;
}
#galeria .content .container dl {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 5%;
  width: 80vw;
  margin: 30px auto;
  font-size: 1.5em;
}
#galeria .content .container dl dt {
  max-width: 320px;
  min-width: 100px;
}
#galeria .content .container dl dt > div {
  z-index: 1;
  position: absolute;
  margin-left: 16px;
  margin-top: 25px;
  padding: 0.5em;
  border-radius: 0.5em;
  font-size: 0.5em;
  color: var(--colorSecondary2);
  background: var(--colorPrimary3);
}
#galeria .content .container dl dt > a > img {
  min-width: 100px;
  width: 100%;
}
#galeria .content .container dl dd {
  width: 60%;
  min-width: 320px;
}
#galeria .content .container dl dd > div {
  margin-top: 10px;
}
#galeria .content .container dl dd > span {
  font-size: 0.6em;
}
#galeria .content .container article {
  max-width: 80vw;
  min-width: 320px;
  width: 80%;
  padding: 0 20px;
  margin-bottom: 4em;
}
#galeria .content .container section {
  display: flex;
  max-width: 80vw;
  min-width: 320px;
  width: 80%;
  margin-bottom: 4em;
  padding: 0 20px;
  gap: 0.1rem 0;
  overflow-x: auto;
  scroll-snap-type: inline mandatory;
}
#galeria .content .container section:focus-visible {
  outline: 0px;
}
#galeria .content .container section::-webkit-scrollbar {
  width: 10px;
}
#galeria .content .container section::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px var(--colorPrimary3);
  border-radius: 20px;
}
#galeria .content .container section::-webkit-scrollbar-thumb {
  background: var(--colorPrimary3);
  border-radius: 20px;
}
#galeria .content .container section::-webkit-scrollbar-thumb:hover {
  background: var(--colorPrimary3);
}
#galeria .content .container section > div {
  z-index: 1;
  position: absolute;
  margin-left: 16px;
  margin-top: 25px;
  padding: 0.5em;
  border-radius: 0.5em;
  color: var(--colorSecondary2);
  background: var(--colorPrimary3);
}
#galeria .content .container section > figure {
  height: min(90vh, 100vw);
  margin: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
  scroll-snap-align: center;
}
#galeria .content .container section > figure > img {
  max-height: 100%;
  object-fit: contain;
  border-radius: 0.5rem;
  display: inline-block;
}

#szobak .content .container article {
  margin: 0 auto;
  justify-content: center;
}
#szobak .content .container article h1 {
  max-width: 960px;
  min-width: 320px;
  padding: 0 20px;
  margin-bottom: 2em;
}
#szobak .content .container article .szoba {
  width: 50%;
  max-width: 480px;
  min-width: 320px;
  margin-bottom: 4em;
  padding: 0 20px;
}
#szobak .content .container article .szoba > img {
  width: 100%;
}
#szobak .content .container article .szoba > h2 {
  font-size: 24px;
  text-transform: uppercase;
  padding: 4px;
}
#szobak .content .container article .szoba > ul {
  width: 100%;
  margin: 30px 0;
  border-spacing: 0 1em;
}
#szobak .content .container article .szoba > ul > li {
  display: table-row;
  color: var(--colorPrimary2);
}
#szobak .content .container article .szoba > ul > li:before {
  padding-right: 1.375em;
}
#szobak .content .container article .szoba > ul > li > span {
  display: table-cell;
  white-space: nowrap;
  padding-right: 1em;
  border-bottom: 2px solid var(--colorPrimary1);
}
#szobak .content .container article .szoba > ul > li > b {
  display: table-cell;
  width: 80%;
  border-bottom: 2px solid var(--colorPrimary1);
}
#szobak .content .container article .szoba > ul > li > h3 {
  display: inline-block;
  text-transform: uppercase;
  color: inherit;
}
#szobak .content .container article .szoba > span {
  display: inline-block;
  min-height: 44px;
  color: var(--colorPrimary2);
  padding: 4px;
}

#szoba .content .container {
  justify-content: center;
  padding-bottom: 2em;
}
#szoba .content .container section {
  display: flex;
  max-width: 640px;
  min-width: 320px;
  width: 45%;
  margin-bottom: 4em;
  gap: 0.1rem 0;
  overflow-x: auto;
  scroll-snap-type: inline mandatory;
}
#szoba .content .container section:focus-visible {
  outline: 0px;
}
#szoba .content .container section::-webkit-scrollbar {
  width: 10px;
}
#szoba .content .container section::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px var(--colorPrimary3);
  border-radius: 20px;
}
#szoba .content .container section::-webkit-scrollbar-thumb {
  background: var(--colorPrimary3);
  border-radius: 20px;
}
#szoba .content .container section::-webkit-scrollbar-thumb:hover {
  background: var(--colorPrimary3);
}
#szoba .content .container section > div {
  z-index: 1;
  position: absolute;
  margin-left: 16px;
  margin-top: 25px;
  padding: 0.5em;
  border-radius: 0.5em;
  color: var(--colorSecondary2);
  background: var(--colorPrimary3);
}
#szoba .content .container section > figure {
  height: min(70vh, 100vw);
  margin: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
  scroll-snap-align: start;
}
#szoba .content .container section > figure > img {
  max-height: 100%;
  object-fit: contain;
  border-radius: 0.5rem;
  display: inline-block;
}
#szoba .content .container article {
  max-width: 360px;
  min-width: 320px;
  width: 45%;
  padding: 0 20px;
  margin-bottom: 4em;
}
#szoba .content .container article h1 {
  padding: 0;
  margin-bottom: 0.5em;
}
#szoba .content .container article > ul {
  border-spacing: 0 1em;
  width: 100%;
}
#szoba .content .container article > ul > li {
  display: table-row;
  color: var(--colorPrimary2);
}
#szoba .content .container article > ul > li:before {
  padding-right: 1.375em;
}
#szoba .content .container article > ul > li > span {
  display: table-cell;
  white-space: nowrap;
  padding-right: 1em;
}
#szoba .content .container article > ul > li > b {
  display: table-cell;
  width: 80%;
}
#szoba .content .container > div {
  width: 100%;
  padding: 20px 8vw 40px;
}

#foglalas .content .container.foglalas {
  margin: 0 auto;
  justify-content: center;
}
#foglalas .content .container.foglalas h1 {
  max-width: 960px;
  min-width: 320px;
  padding: 0 20px;
  margin-bottom: 1em;
}
#foglalas .content .container.foglalas > div {
  min-width: 320px;
  width: 100%;
  justify-content: space-evenly;
  gap: 10px;
  margin-bottom: 2em;
}
#foglalas .content .container.foglalas > div > a {
  max-width: 320px;
  width: 15vw;
}
#foglalas .content .container.foglalas > div > a > img {
  width: 100%;
}
#foglalas .content .container.foglalas > div > a > h2 {
  text-transform: uppercase;
}
#foglalas .content .container.foglalas > div > form {
  max-width: 320px;
  min-height: 80px;
  background-color: var(--colorPrimary3);
  color: var(--colorSecondary2);
}
#foglalas .content .container.foglalas > div > form > ul {
  display: flex;
  justify-content: space-around;
  flex-flow: row wrap;
  -webkit-flex-flow: row wrap;
  gap: 10px;
  align-items: end;
  padding: 5px 10px;
}
#foglalas .content .container.foglalas > div > form > ul > li:first-of-type {
  width: 100%;
}
#foglalas .content .container.foglalas > div > form > ul > li:first-of-type h3 {
  color: var(--colorSecondary2);
}
#foglalas .content .container.foglalas > div > form > ul > li:first-of-type h3 b {
  color: lawngreen;
}
#foglalas .content .container.foglalas > form > table {
  table-layout: auto;
  min-width: 320px;
  margin: 0 3vw 2em;
}
#foglalas .content .container.foglalas > form > table > thead {
  position: sticky;
  top: 0;
  z-index: 1;
}
#foglalas .content .container.foglalas > form > table > thead th:nth-child(1) {
  min-width: 320px;
}
#foglalas .content .container.foglalas > form > table > thead th:nth-child(2) {
  min-width: 75px;
}
#foglalas .content .container.foglalas > form > table > thead th:nth-child(3) {
  min-width: 140px;
}
#foglalas .content .container.foglalas > form > table > thead th:nth-child(4) {
  min-width: 105px;
}
#foglalas .content .container.foglalas > form > table > thead th:nth-child(5) {
  min-width: 320px;
}
#foglalas .content .container.foglalas > form > table > tbody td > div.lakoegysegvan {
  display: none;
}
#foglalas .content .container.foglalas > form > table > tbody td > div.lakoegysegvan.visible {
  display: block;
}
#foglalas .content .container.foglalas > form > table > tbody td > div.lakoegysegvan span {
  margin: 0 4px;
  font-style: italic;
}
#foglalas .content .container.foglalas > form > table > tbody td > div.lakoegysegvan .btn {
  margin: 16px 4px 0;
  font-size: 2em;
  width: -webkit-fill-available;
}
#foglalas .content .container.foglalas > form > table > tbody td > div.lakoegysegkell {
  display: none;
  margin: 4px;
  padding: 16px 4px;
  font-size: 2em;
  text-align: center;
  background-color: var(--colorPrimary3);
  color: var(--colorSecondary2);
}
#foglalas .content .container.foglalas > form > table > tbody td > div.lakoegysegkell.visible {
  display: block;
}
#foglalas .content .container.foglalas > form > table > tbody td > dl.lakoegysegvan {
  display: none;
}
#foglalas .content .container.foglalas > form > table > tbody td > dl.lakoegysegvan.visible {
  display: flex;
}
#foglalas .content .container.foglalas > form > table > tbody td > dl.lakoegysegvan .oar {
  font-size: 2em;
  color: var(--colorTertiary1);
}
#foglalas .content .container.foglalas > form > table > tbody td > dl dt {
  width: 40%;
  text-align: right;
  padding-right: 1em;
  margin: 8px 0 0;
}
#foglalas .content .container.foglalas > form > table > tbody td > dl dd {
  width: 60%;
  margin: 8px 0 0;
}
#foglalas .content .container.foglalas > form > table > tbody td > dl dd.lakoe {
  color: var(--colorPrimary3);
  font-weight: bold;
}
#foglalas .content .container.foglalas > form > table > tbody td > dl .dlutolso {
  padding-top: 1em;
}
#foglalas .content .container.foglalas > form > table > tbody td.foglalasbox {
  position: sticky;
  top: 32px;
}
#foglalas .content .container.foglalas > div.foglalt {
  min-width: 320px;
  width: 94%;
  margin: 0 3vw 2em;
  overflow-x: auto;
  scroll-snap-type: inline mandatory;
}
#foglalas .content .container.foglalas > div.foglalt:focus-visible {
  outline: 0px;
}
#foglalas .content .container.foglalas > div.foglalt::-webkit-scrollbar {
  width: 10px;
}
#foglalas .content .container.foglalas > div.foglalt::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px var(--colorPrimary3);
  border-radius: 20px;
}
#foglalas .content .container.foglalas > div.foglalt::-webkit-scrollbar-thumb {
  background: var(--colorPrimary3);
  border-radius: 20px;
}
#foglalas .content .container.foglalas > div.foglalt::-webkit-scrollbar-thumb:hover {
  background: var(--colorPrimary3);
}
#foglalas .content .container.foglalas > div.foglalt > table {
  margin-bottom: 10px;
  scroll-snap-align: start;
}
#foglalas .content .container.lefoglalom {
  margin: 0 3vw;
  justify-content: ceter;
}
#foglalas .content .container.lefoglalom h1 {
  min-width: 320px;
  margin-bottom: 1em;
  padding: 0;
}
#foglalas .content .container.lefoglalom > div {
  min-width: 320px;
  width: 30%;
}
#foglalas .content .container.lefoglalom > div > dl .oar {
  width: 100%;
  margin: 8px 2% 0;
  font-size: 2em;
  text-align: center;
  color: var(--colorTertiary1);
  background-color: chartreuse;
}
#foglalas .content .container.lefoglalom > div > dl dt {
  width: 40%;
  text-align: right;
  padding-right: 1em;
  margin: 8px 0 0;
}
#foglalas .content .container.lefoglalom > div > dl dd {
  width: 60%;
  margin: 8px 0 0;
}
#foglalas .content .container.lefoglalom > div > dl dd.lakoe {
  color: var(--colorPrimary3);
  font-weight: bold;
}
#foglalas .content .container.lefoglalom > form {
  min-width: 320px;
  width: 70%;
  justify-content: space-evenly;
  gap: 1%;
}
#foglalas .content .container.lefoglalom > form div.szabaly {
  width: 100%;
  margin-top: 10px;
}
#foglalas .content .container.lefoglalom > form .btn {
  margin: 16px 4px 0;
  font-size: 2em;
  width: -webkit-fill-available;
}
#foglalas .foglaltF {
  background-color: red;
}
#foglalas .foglaltS {
  background-color: green;
}
#foglalas .foglaltT {
  background: linear-gradient(90deg, red 0%, red 42%, green 42%, green 100%);
}
#foglalas .foglaltE {
  background: linear-gradient(90deg, green 0%, green 58%, red 58%, red 100%);
}
#foglalas .foglaltC {
  background: linear-gradient(90deg, red 0% 42%, #999 42% 58%, red 58% 100%);
}

.dialog {
  z-index: 99;
  position: fixed;
  top: 40px;
  max-width: 320px;
  margin: 0 calc(50% - 160px);
  padding: 20px;
  text-align: center;
  color: var(--colorTertiary1);
  background-color: chartreuse;
}

.flex {
  display: flex;
  justify-content: start;
  flex-flow: row wrap;
  -webkit-flex-flow: row wrap;
  align-items: start;
}

.error {
  position: absolute;
  z-index: 1;
  top: 0;
  margin-top: 10px;
  display: block;
  width: inherit;
  padding: 6px;
  border-radius: 4px;
  background-color: #f00;
  color: #ffbc2c;
}

sup {
  color: #f00;
}

#trslider {
  display: block;
  width: 100%;
  position: relative;
  overflow: hidden;
  text-align: center;
}
#trslider > div {
  z-index: 1;
  position: absolute;
  margin-left: 16px;
  margin-top: 25px;
  padding: 0.5em;
  border-radius: 0.5em;
  color: var(--colorSecondary2);
  background: var(--colorPrimary3);
}

#trslider > a {
  display: none;
}

#trslider > a:first-child {
  display: inline-block;
}

#trslider > a > img {
  width: 100%;
}

.gdprplusckb {
  display: none;
  position: fixed;
  z-index: 9999;
  bottom: 40px;
  left: 0;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  color: #fff;
  background-color: var(--colorPrimary3);
}
.gdprplusckb > div > a {
  display: block;
  position: absolute;
  right: 0;
  width: 30px;
  height: 30px;
  padding: 4px;
  margin: -15px -15px 0 0;
  border-radius: 50%;
  font-size: 20px;
  line-height: 20px;
  text-align: center;
  border-color: #8bb336;
  background-color: #8bb336;
  color: #999;
}
.gdprplusckb > div > a:hover {
  text-decoration: none;
  color: #f00;
}
.gdprplusckb > div > div {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 100%;
  padding: 15px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: #fff;
}
.gdprplusckb > div > div:last-child {
  border: 0;
}
.gdprplusckb > div > div > span {
  display: flex;
  align-items: center;
}
.gdprplusckb > div > div > span input {
  display: inline-block;
  margin-right: 4px;
}
.gdprplusckb > div > div > a {
  margin-left: 4px;
}

.gdprplusbox {
  display: none;
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.gdprplusbox > div {
  position: relative;
  z-index: 1;
  max-width: 90vw;
  margin: 10vh auto;
  border-radius: 6px;
  color: #fff;
  background-color: var(--colorPrimary3);
}
.gdprplusbox > div > a {
  display: block;
  position: absolute;
  right: 0;
  width: 30px;
  height: 30px;
  padding: 4px;
  margin: -15px -15px 0 0;
  border-radius: 50%;
  font-size: 20px;
  line-height: 20px;
  text-align: center;
  background-color: #8bb336;
  border-color: #8bb336;
  color: #fff;
}
.gdprplusbox > div > a:hover {
  text-decoration: none;
  color: #f00;
}
.gdprplusbox > div > span {
  display: block;
  padding: 4px 15px;
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #ff771f;
}
.gdprplusbox > div > div {
  max-width: 100%;
  max-height: 80vh;
  padding: 15px;
  overflow: hidden;
  overflow-y: auto;
}
.gdprplusbox > div > div h5 {
  font-size: 18px;
  margin: 6px 0;
}
.gdprplusbox > div > div h6 {
  font-size: 16px;
  margin: 4px 0;
}
.gdprplusbox > div > div p {
  font-size: 14px;
  margin: 2px 0;
}
@media only screen and (min-width: 480px) {
  .gdprplusbox > div {
    max-width: 60vw;
  }
}

.logo {
  max-width: 100%;
}

.header-nav {
  width: 100%;
}

.header-links {
  text-align: center;
  margin-right: 0;
  width: 100%;
}

@media only screen and (min-width: 480px) {
  .header-nav {
    width: 320px;
  }
}
@media only screen and (min-width: 640px) {
  .header-links {
    width: auto;
    margin-right: 40px;
  }
  footer > div {
    width: 30%;
    min-width: calc(320px - 4vw);
  }
}
@media only screen and (min-width: 740px) {
  .foglalas > ul > li:first-child {
    width: 20%;
  }
}
::-webkit-input-placeholder {
  color: #999;
}

:-moz-placeholder {
  color: #999;
}

::-moz-placeholder {
  color: #999;
}

:-ms-input-placeholder {
  color: #999;
}

.disabled,
input[text]:read-only,
:disabled {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAAIklEQVQIW2NkQAKrVq36zwjjgzhhYWGMYAEYB8RmROaABADeOQ8CXl/xfgAAAABJRU5ErkJggg==) repeat;
}

input,
select,
table {
  font-size: 1em;
}

textarea {
  font-size: 1.15em;
}

input[type=date],
input[type=email],
input[type=password],
input[type=text] {
  width: 100%;
  height: 2.25em;
  line-height: 2.25em;
  font-weight: 500;
  text-indent: 0.2em;
  text-align: left;
  vertical-align: middle;
  outline: none;
  border: 1px solid #999;
  background-color: #fff;
  color: #676a6c;
}
input[type=date]:focus,
input[type=email]:focus,
input[type=password]:focus,
input[type=text]:focus {
  background-color: #fff;
  border: 1px solid #0f8469;
  color: #000;
}

li {
  position: relative;
}

textarea {
  width: 100%;
  height: 10em;
  font-weight: 500;
  text-indent: 0.2em;
  border: 1px solid #999;
  background-color: #fff;
  color: #676a6c;
}
textarea:focus {
  background-color: #fff;
  border: 1px solid #0f8469;
  color: #000;
}
textarea:focus {
  position: inherit;
  z-index: 1;
}

select {
  width: 100%;
  height: 2.25em;
  line-height: 2.25em;
  cursor: pointer;
  border: 1px solid #999;
  background-color: #fff;
  color: #676a6c;
  /*IE*/
}
select:focus {
  background-color: #fff;
  border: 1px solid #0f8469;
  color: #000;
}
select::-ms-expand {
  display: none;
}
select option:nth-child(even) {
  background-color: #ddd;
}

.form-select[data-icon] {
  position: relative;
  display: inline-block;
}
.form-select[data-icon]:after {
  position: absolute;
  text-align: center;
  font-family: tolgyfapanzio;
  font-weight: 500;
  pointer-events: none;
  font-size: 1em;
  vertical-align: middle;
  content: attr(data-icon);
  width: 2.25em;
  height: 2.25em;
  line-height: 2.25em;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: #999;
  color: #fff;
}
.form-select[data-icon].padr:after, .form-select[data-icon].padlr:after {
  right: 4px;
}

[data-on],
[data-to] {
  position: relative;
  display: inline-block;
}

[data-on]:before {
  position: absolute;
  text-align: center;
  font-family: tolgyfapanzio;
  font-weight: 500;
  pointer-events: none;
  font-size: 1em;
  vertical-align: middle;
  content: attr(data-on);
  width: 2.25em;
  height: 2.25em;
  line-height: 2.25em;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  right: auto;
}
[data-on] > input {
  padding-left: 2.25em;
}
[data-on].padl:before, [data-on].padlr:before {
  left: 4px;
}

[data-to]:after {
  position: absolute;
  text-align: center;
  font-family: tolgyfapanzio;
  font-weight: 500;
  pointer-events: none;
  font-size: 1em;
  vertical-align: middle;
  content: attr(data-to);
  width: 2.25em;
  height: 2.25em;
  line-height: 2.25em;
  top: 0;
  right: 0;
  bottom: 0;
  top: auto;
  bottom: auto;
  font-size: inherit;
}
[data-to] > input {
  padding-right: 2.25em;
}
[data-to].padr:after, [data-to].padlr:after {
  right: 4px;
}

.padlilr [data-on]:before {
  left: 4px;
}

.padlilr [data-to]:after {
  right: 4px;
}

.bgonto[data-to]:after,
.bgonto[data-on]:before {
  color:fff;
  background-color: #999;
}

.padlr.h2on[data-on]:before {
  left: 4px;
}

.h2on[data-on]:before {
  width: 3.375em;
}

.h2on[data-on] > input {
  padding-left: 3.375em;
}

input[type=radio],
input[type=checkbox] {
  display: none;
}
input[type=radio] + label,
input[type=checkbox] + label {
  display: inline-block;
  position: relative;
  height: calc(2.25em - 4px);
  line-height: calc(2.25em - 4px);
  cursor: pointer;
}

.cheaft input + label {
  padding-right: 2.45em;
  margin-left: 0.8em;
}
.cheaft input + label::after {
  right: 0;
  position: absolute;
  text-align: center;
  font-family: tolgyfapanzio;
  font-weight: 500;
  pointer-events: none;
  font-size: 1em;
  vertical-align: middle;
  content: "";
  display: inline-block;
  width: calc(2.25em - 2px);
  height: calc(2.25em - 2px);
  border: 1px solid #999;
  background-color: #fff;
}

.chebef input + label {
  padding-left: 2.45em;
}
.chebef input + label::before {
  left: 0;
  position: absolute;
  text-align: center;
  font-family: tolgyfapanzio;
  font-weight: 500;
  pointer-events: none;
  font-size: 1em;
  vertical-align: middle;
  content: "";
  display: inline-block;
  width: calc(2.25em - 2px);
  height: calc(2.25em - 2px);
  border: 1px solid #999;
  background-color: #fff;
}

.chebef input:checked + label:before,
.cheaft input:checked + label:after {
  content: var(--icon-check);
  color: #676a6c;
}

.radiogomb label, .checlick label {
  padding-left: 1em;
  padding-right: 1em;
  margin-right: 0.2em;
  margin-bottom: 0.2em;
  background-color: #000;
  color: #676a6c;
}
.radiogomb label:before, .checlick label:before, .radiogomb label:after, .checlick label:after {
  display: none;
}
.radiogomb input[type=radio]:checked + label, .checlick input[type=radio]:checked + label {
  background-color: #676a6c;
  color: #999;
}

.checlick > input[type=checkbox] + label, .checlick > input[type=radio] + label {
  display: block;
}
.checlick > input[type=checkbox] + label:before, .checlick > input[type=radio] + label:before {
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\f105";
  font-family: tolgyfapanzio;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  margin-right: 1em;
}
.checlick > input[type=checkbox]:checked + label:before, .checlick > input[type=radio]:checked + label:before {
  content: "\f107";
}
.checlick > input[type=checkbox]:checked + label + div, .checlick > input[type=radio]:checked + label + div {
  display: block;
}
.checlick > div {
  display: none;
}

.btn {
  display: inline-block;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 1px solid transparent;
  border-radius: 4px;
  color: #fff;
  background-color: var(--colorTertiary1);
}
.btn:hover {
  text-decoration: none;
  color: #fff;
  background-color: #0782df;
}

[style^="--icon:"]:before,
[style*=" --icon:"]:before{
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: tolgyfapanzio;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  margin-right: 0.2em;
}

[style^="--icon:"]:before,
[style*=" --icon:"]:before {
  content: var(--icon);
}

.iconbox {
  font-size: 2em;
  text-align: center;
}

.iconf {
  font-family: tolgyfapanzio;
}

a.clickicon {
  z-index: 1;
  position: absolute;
  display: inline-block;
  width: 1.125em;
  line-height: 1.25em;
  font-size: 2.25em;
  font-family: tolgyfapanzio;
  font-style: normal;
  font-weight: normal;
  text-align: center;
  text-decoration: none;
  background-color: var(--colorTertiary1);
  color: #fff;
}
a.clickicon + input, a.clickicon + select {
  padding-left: 2.25em;
}
a.clickicon.iconin {
  font-size: x-large;
}
a.clickicon:hover {
  background-color: #0782df;
  color: #fff;
  cursor: default;
}

/*
.clickicon + a:before{
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: attr(data-icon);
  font-family: tolgyfapanzio;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  margin-right: .2em;
}
*/
table {
  min-width: 320px;
  border-collapse: collapse;
}
table thead th {
  background-color: var(--colorPrimary3);
  color: var(--colorSecondary2);
  text-align: center;
  font-weight: normal;
  white-space: nowrap;
  padding: 4px;
}
table tfoot {
  background-color: #999;
  color: #676a6c;
  font-weight: normal;
  white-space: nowrap;
}
table td {
  padding: 4px;
}
table td:last-child {
  padding-left: 4px;
  padding-right: 4px;
}
table td .icon {
  font-size: calc(1em + 4px);
}
table td .icon:hover:before {
  color: #676a6c;
}

table, td, th {
  border: 1px solid #999;
}/*# sourceMappingURL=screen.css.map */