        <link href="/spritzer/ui/ui-gallery.css" rel="stylesheet">
        <video playsinline autoplay muted loop>
          <source id="mp4" src="/items/odhatter.mp4" type="video/mp4">
          Ez a böngésző program nem támogatja a videó tartalmat. Pr<PERSON><PERSON><PERSON>ld frissíteni.

        </video>
        <div class="hero">
          <h1>OTTHON DUNAKANYAR</h1>
          Skandináv életérzés a Pilis és a Börzsöny ölelésében
        </div>
        <div id="ahaz">
          <h2>A ház</h2>
          <dl>
            <dt>
              <img src="<?= DIRECTORY ?>pic0053.jpg" loading="lazy">
            </dt>
            <dd>
              <p style="margin-top: 0">
                Az Otthon Dunakanyar azzal a céllal épült, hogy kompromisszummentes pihenést és kikapcsolódást nyújtson vendégeinek a nálunk töltött idő alatt. A tervezés és kivitelezés során nem pusztán egy szálláshelyet szerettünk volna létrehozni, sokkal inkább egy olyan házat, mely az igazi otthonosság érzését kínálja. Mindezt kiemelkedő színvonalon, letisztult, egyedi formavilággal, összhangban a környékkel és a természettel.
              </p>
              <p> 
                Mi nem csupán szállást biztosítunk, hanem komplex élménycsomagot kínálunk vendégeinknek, felkínálva minden izgalmas programot, melyet a ház, a környék és a régió kínál az ide látogatóknak.
              </p>
            </dd>
          </dl>
        </div>

        <div id="szolg">
          <h2>Szolgáltatások</h2>
          <dl>
            <dd>
                <p style="margin-top: 0">
                Vendégházunk maximum 6 fő számára kínál kényelmes, otthonos, stílusos pihenést a Dunakanyar szívében, Dömös szélén, közvetlenül a Pilis lankáin. Hozzánk tényleg csak megérkezned kell, egy átgondolt, tiszta és jól felszerelt házzal várunk. Nálunk teljes a nyugalom, nem zavar a szomszéd, csak ti lehettek. Valamennyi kaput és a bejárati ajtót is személyre szabott egyedi kóddal tudod nyitni, így rugalmas az érkezésetek is. A földszinten egy nagy légterű amerikai konyhás nappali-étkező, a fürdőszoba és külön wc, valamint egy elegáns, privát franciaágyas hálószoba vár, valamint itt található a nappalival szinte egybeolvadva, a tökéletes reggelik és a hosszú beszélgetések és borozások helyszíne a 30 m2-es panorámás teraszunk is. A galériára érve a franciaágyból, vagy a gyerekek nagy kedvencéről, a beépített kuckó ágyból élvezhetitek a végtelen panorámát a Duna kanyarulatára és a környező tájra.
              </p>
              <p>
                Parkosított kertünk 900 m2, a szabadtéri sütés-főzéshez, szalonnasütéshez tűzrakóval és nagy, füves grunddal a szabadtéri játékokhoz.
              </p>
              <p> 
                A főépület mögött, nyugat felé tájolva áll a szaunaház a 4 fős panorámás finn szaunával, a hozzá tartozó pihenővel, zuhannyal és panorámás terasszal a tökéletes ellazulás és feltöltődés helyszíneként. 
              </p>
            </dd>
            <dt>
              <img src="<?= DIRECTORY ?>pic0029.jpg" loading="lazy">
            </dt>
          </dl>
          <ul>
            <li>
              <i class="fa-solid fa-kitchen-set"></i>
              <h3>Felszerelt konyha</h3>
            </li>
            <li>
              <i class="fa-solid fa-mug-saucer"></i>
              <h3>Nespresso kapszula</h3>
            </li>
            <li>
              <i class="fa-solid fa-fan"></i>
              <h3>Klíma</h3>
            </li>
            <li>
              <i class="fa-solid fa-wifi"></i>
              <h3>Wifi</h3>
            </li>
            <li>
              <i class="fa-solid fa-tv"></i>
              <h3>Streaming csatornák</h3>
            </li>
            <li>
              <i class="fa-solid fa-volume-high"></i>
              <h3>Speaker</h3>
            </li>
            <li>
              <i class="fa-solid fa-book"></i>
              <h3>Házikönyvtár</h3>
            </li>
            <li>
              <i class="fa-solid fa-panorama"></i>
              <h3>Panorámás terasz</h3>
            </li>
            <li>
              <i class="fa-solid fa-sun-plant-wilt"></i>
              <h3>Parkosított kert</h3>
            </li>
            <li>
              <i class="fa-solid fa-spa"></i>
              <h3>Finnszauna</h3>
            </li>
            <li>
              <i class="fa-solid fa-tree"></i>
              <h3>Erdő mellett</h3>
            </li>
            <li>
              <i class="fa-solid fa-dice"></i>
              <h3>Társasjátékok</h3>
            </li>
            <li>
              <i class="fa-solid fa-fire"></i>
              <h3>Tűzrakó</h3>
            </li>
            <li>
              <i class="fa-solid fa-drum-steelpan"></i>
              <h3>Bográcsozó</h3>
            </li>
            <li>
              <i class="fa-solid fa-square-parking"></i>
              <h3>Zárt parkoló</h3>
            </li>
            <li>
              <i class="fa-solid fa-table-cells-column-lock"></i>
              <h3>Kulcs nélküli bejutás</h3>
            </li>
          </ul>
        </div>
        <div id="galeria">
          <h2>Galéria</h2>
          <div data-gallery="{}">
            <img src="<?= DIRECTORY ?>pic0004.jpg" loading="lazy">
            <img src="<?= DIRECTORY ?>pic0009.jpg" loading="lazy">
            <img src="<?= DIRECTORY ?>pic0017.jpg" loading="lazy">
            <img src="<?= DIRECTORY ?>pic0029.jpg" loading="lazy">
            <img src="<?= DIRECTORY ?>pic0025.jpg" loading="lazy">
            <img src="<?= DIRECTORY ?>pic0006.jpg" loading="lazy">
            <img src="<?= DIRECTORY ?>pic0043.jpg" loading="lazy">
            <img src="<?= DIRECTORY ?>pic0039.jpg" loading="lazy">
          </div>
          <a href="/galeria" class="btn">Tovább >>></a>
        </div>
        <script type="module">
          import {gallery} from '/spritzer/index.js'
          gallery()
        </script>
        <div class="velemeny">
          <h2>Vendégeink mondták</h2>
          <div>
            <?php if( $res->ratings ){ ?>
            <?php   foreach( $res->ratings as $rating ){ ?>
              <?= $rating->provider.' értékelés '.$rating->max_rating.'/'.round( $rating->rating, 0 ) ?>
              <i style="--icon:var(--icon-star)"></i> <?= $rating->reviews_number ?> vélemény<br>
            <?php } }else{ ?>
              <i style="--icon:var(--icon-star)"></i>
              <i style="--icon:var(--icon-star)"></i>
              <i style="--icon:var(--icon-star)"></i>
              <i style="--icon:var(--icon-star)"></i>
              <i style="--icon:var(--icon-star)"></i>
            <?php } ?>
          </div>
          <section>
            <?php if( $res->reviews ){ ?>
            <?php   foreach( $res->reviews as $review ){ ?>
              <figure>
                <p><?= $review->review ?></p>
                <?= $review->provider.' '.substr( $review->when_was, 0, 10 ) ?> <a href="#" class="btn btn-small"><?= $review->author_name ?></a>
              </figure>
            <?php } } ?> 
          </section>
          <?php /*
          <div class="step"><span class="btn-prev"><<</span> <span class="btn-next">>></span></div>
          */ ?>
          <script>
            document.addEventListener( 'DOMContentLoaded', () => {           
              let clicked = 0
              let scroll_param = {behavior: "smooth", block: "start"}
              const viewelements = document.querySelectorAll( '.velemeny > section > figure' )
              //viewelements[clicked].scrollIntoView( scroll_param )
              document.querySelectorAll( '.btn-next, .btn-prev' ).forEach( element => {
                element.addEventListener( 'click', ( ec ) => {
                  if( ec.target.className === 'btn-next' )
                    clicked = clicked + 1
                  else
                    clicked = clicked - 1
                  if( clicked < 0 ) clicked = 0
                  if( clicked >= viewelements.length ) clicked = viewelements.length - 1
                  viewelements[clicked].scrollIntoView( scroll_param )
                })
              })
            })
          </script>
        </div>
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" loading="lazy">
        <div id="map">
          <h2>Hol találsz minket</h2>
          <div style="text-align: center;margin-bottom: 3rem">
            2027 Dömös, Szuha utca 22.<br>
            <EMAIL><br>
            +36 20 912 7170<br>
            <a href="https://www.facebook.com/profile.php?id=61556278851299" style="--icon:var(--icon-facebook)"></a>
            &nbsp;&nbsp;&nbsp;
            <a href="https://www.instagram.com/otthon_dunakanyar" style="--icon:var(--icon-instagram)"></a>
          </div>
          <div id="map-container"></div>
          <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
          <script>
            let
              map = L.map( 'map-container' ).setView( [47.7668608, 18.9006245], 15 ),
              tiles = L.tileLayer( 'https://tile.openstreetmap.org/{z}/{x}/{y}.png' )
                       .addTo( map ),
              marker = L.marker( [47.76599, 18.89762] )
                        .addTo( map )
                        .bindPopup( '2027 Dömös, Szuha utca 22.' ).openPopup()
          </script>
        </div>
        <div class="hirlevel">
          <div>
            <div class="opacitybox"></div>
            <i>Nem szemetelünk!</i>
            <p>
              Iratkozz fel hírlevelünkre, hogy első kézből értesülj
              kedvezményeinkről, programjainkról, újdonságainkról!
            </p>
            <form name="subscriber">
              <ul class="formbox">
                <li class="form col5"><input type="text" name="name" placeholder=""><label>Vezetéknév</label></li>
                <li class="form col5"><input type="text" name="first_name" placeholder=""><label>Keresztnév</label></li>
                <li class="form col0"><input type="text" name="email" placeholder="" required><label>E-mail cím</label></li>
                <li class="form col0 chebef">
                  <input type="checkbox" name="gdpr" id="gdpr" required>
                  <label for="gdpr">Az <a href="<?= DIRECTORY ?>privacy.pdf" target="_blank">Adatkezelési tályékoztatóban</a> foglaltakat elolvastam, az adataim kezeléséhez hozzájárulok</label>
                </li>
                <li class="form col0"><input type="submit" value="Feliratkozom"></li>
              </ul>
            </form>
            <script type="module">
              import {$} from './spritzer/index.js'
              $( 'form[name="subscriber"]' ).addEventListener( 'submit', ( e ) => {
                e.preventDefault()
                let formData = new FormData( e.target )
                fetch( '/apicall/subscriber', {
                  method: 'POST',
                  headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify( Object.fromEntries( formData ) )
                } )
                document.querySelector( '.hirlevel' ).innerHTML = 
                  '<div><div class="opacitybox"></div><p>Köszönjük a feliratkozást!</p></div>'
              } )
            </script>
          </div>
        </div>
        <?php if( $res->advertall ?? 0 ){ ?>
        <?= $res->advertall->popup ?>
        <?php } ?>
