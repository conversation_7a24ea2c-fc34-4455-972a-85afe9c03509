import { $, fullScreen, getCharCode } from '../default.js';
import { loadHTML2render } from '../asynccom/asynccom.js';
import { loader } from '../ui/ui.js';

export function dialog( param = null ) {
  let dialog  = document.createElement( 'dialog' ),
      wrapper = document.createElement( 'div' ),
      type = param.type ? param.type.split(':') : param.loadFile ? ['modal'] : ['tooltip'],
      dialogBackground = null
  dialog.append( wrapper )
  if( param.id ) dialog.setAttribute( 'id', param.id )
  dialog.dataset.dialog = type[0] + ( type[1]? '-' + type[1] : '' )
  dialog.addEventListener( 'click', eventManager )
  document.addEventListener( 'keyup', eventManager )
  if( ['dialog', 'modal'].includes( type[0] ) ){
    if( ['modal'].includes( type[0] ) ){
      dialogBackground = document.createElement( 'div' ),
      dialogBackground.classList.add( 'dialog-background' )
      if( param.isCloseBack ) dialogBackground.addEventListener( 'click', eventManager )
    }
    if( param.events )
      param.events.forEach( eventType => {
        dialog.addEventListener( eventType, eventManager )
      } )
  }
  
  if( param.width ) dialog.style.width = param.width
  if( param.marginTopX ){ // TODO Nem biztos, hogy kell
    let style = dialog.currentStyle || window.getComputedStyle( dialog )
    dialog.style.marginTop = 'calc('+ param.marginTopX +' * '+ style.marginTop +')'
  }
  if( ['modal'].includes( type[0] ) ) $( 'body' ).append( dialogBackground )
  $( param.parent ? param.parent : 'body' ).append( dialog )
  dialog.show()

  // felépítés
  if( param.loadFile && ['dialog', 'modal'].includes( type[0] ) ){
    dialog.setAttribute( 'data-param', JSON.stringify( param ) )
    wrapper.append( loader( 'spinner', '3s' ) )

    async function lh2r(){
      if( await loadHTML2render( { loadFile: param.loadFile, html: wrapper } ) )
        if( param.eventManagers && param.eventManagers.afterBuilding ){
          let plusEventManagers = param.eventManagerFunctions( param.eventManagers.afterBuilding )
          if( typeof plusEventManagers === 'object' )
            param.eventManagers = {...param.eventManagers, ...plusEventManagers}
        }
    }
    lh2r()
  }else{
    let html = '' 
    if( param.logo || param.title || param.iconClose || param.iconFullscreen ){
      html += '<header>'
      if( param.logo && ['snackbar', 'dialog', 'modal'].includes( type[0] ) )  
        html += ( param.logo[0] == '-' )?
          '<i style="--icon:var('+ param.logo +')"></i>'
        : 
          '<img src="'+ param.logo +'">'
      if( ( param.title && !['tooltip'].includes( type[0] ) ) || ['status'].includes( type[0] ) ){
        if( type[0] == 'status' && !param.title )
          switch( type[1] ){
            case 'error': html += '<h6>Error</h6>'; break;
            case 'warning': html += '<h6>Warning</h6>'; break;
            case 'success': html += '<h6>Success</h6>'; break;
            case 'info': html += '<h6>Info</h6>'; break;
          }
        html += '<h6>'+ param.title +'</h6>'
      }
      if( param.iconClose || ( param.iconFullscreen && ['dialog', 'modal'].includes( type[0] ) ) ){
        html += '<div>'
        if( param.iconFullscreen && ['dialog', 'modal'].includes( type[0] ) ){
          html += '<span class="fullscreen"'+
                       ' style="--icon:var('+ param.iconFullscreen.split('|')[0] +')"'+
                       ' data-fsicon="'+ param.iconFullscreen +'">'+
                  '</span>'
        }
        if( param.iconClose ) html += '<span class="close" style="--icon:var('+ param.iconClose +')"></span>'
        html += '</div>'
      }
      html += '</header>'
    }

    if( param.content ) html += '<section>'+param.content+'</section>'
    
    if( ( param.isBtnClose || param.isBtnCallback ) && ['snackbar', 'dialog', 'modal'].includes( type[0] ) ){
      if( !param.btnCallback ) param.btnCallback = 'Tovább'
      if( !param.btnClose ) param.btnClose = 'Bezár'
      html += '<footer>'
      if( param.isBtnClose ) html += '<button class="close">'+param.btnClose+'</button>'
      if( param.isBtnCallback ) html += '<button class="callback">'+param.btnCallback+'</button>'
      html += '</footer>'
    }

    wrapper.innerHTML = html
    if( param.eventManagers && param.eventManagers.afterBuilding && param.eventManagerFunctions
        && ['dialog', 'modal'].includes( type[0] ) ){
      console.log( 'dialog afterBuilding',param.eventManagers.afterBuilding )
      param.eventManagerFunctions( param.eventManagers.afterBuilding )
    }
  }
  
  if( param.timeDelay && !['dialog', 'modal'].includes( type[0] ) ) setTimeout( closeDialog, param.timeDelay )

  function closeDialog(){
    if( param.eventManagers && param.eventManagers.close && param.eventManagerFunctions )
      param.eventManagerFunctions( param.eventManagers.close )
    dialog.remove()
    if( ['modal'].includes( type[0] ) ) dialogBackground.remove()
  }

  function eventManager( event ){
    if( event.target.closest( '.close' ) || // btnClose gomb esemény
        ( param.isESCkey && getCharCode( event ) == 27 ) || // ESC billentyű esemény
        ( param.isCloseBack && event.target.closest( '.dialog-background' ) ) ) // panelen kivűli kattintás
      closeDialog()
    else if( event.target.closest( '.fullscreen' ) ) fullScreen( event.target ) // fullscreen esemény
    else if( event.target.closest( '.callback' ) ){ // btn_callback gomb esemény
      if( param.eventManagers && param.eventManagers.callback && param.eventManagerFunctions )
        param.eventManagerFunctions( param.eventManagers.callback )
      if( param.isKeepOpen && param.is_ko ) closeDialog()
    }else{
      if( param.eventManagers && param.eventManagers[event.type] && param.eventManagerFunctions )
        // egyéb paraméterben megadott események /eventManagers/
        param.eventManagers[event.type].forEach( ( eventManager ) => {
          if( event.target.closest( eventManager[0] ) )
            param.eventManagerFunctions( eventManager )
        } )
    }
  }
}

export function dialogRebuild( id ){
  const dialog = document.querySelector( 'dialog[id="'+ id +'"]' )
  if( dialog ){
    const
      wrapper = dialog.querySelector( '&>div' ),
      param = JSON.parse( dialog.dataset.param )
    wrapper.innerHTML = ''
    loadHTML2render( {loadFile: param.loadFile, html: wrapper} )
  }
}

export function closeDialog( id ){
  const
    dialog = $( 'dialog[id="'+ id +'"]' ),
    dialogBackground = $( '.dialog-background' )
  if( dialog ){
    dialog.remove()
    if( dialogBackground ) dialogBackground.remove()
  }
}