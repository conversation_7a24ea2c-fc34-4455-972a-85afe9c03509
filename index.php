<?php
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);
session_start();

$url = ( strpos( $_SERVER['REQUEST_URI'], 'index.php' ) !== false )
       ? substr( $_SERVER['REQUEST_URI'], 0, strpos( $_SERVER['REQUEST_URI'], 'index.php' ))
       : $_SERVER['REQUEST_URI'];
$url = ( $url[0] == '/' )? (( strlen( $url ) > 1 )? substr( $url, 1 ) : '' ) : $url;
$url = ( strpos( $url, '?' ) !== false )? substr( $url, 0, strpos( $url, '?' )) : $url;
$url = ( substr( $url, -1 ) == '/' )? substr( $url, 0, -1 ) : $url;
$url_request = '/'.$url;
$url = explode( '/', $url );
foreach( $url as $k => $e ) if( strlen( $e ) == 0 ) unset( $url[$k] );
$route = ( count( $url ))? $url : ['home'];

switch( $route[0] ){
  case 'galeria':
    
  break;
  case 'etlap':
    $vw['title'] = 'Lelle Étterem és Pizzéria - Balatonlelle - Étlap';
    $vw['description'] = 'Lelle Étterem és Pizzéria - Balatonlelle. Étlapon a teljes választékunk.';
    $vw['page'] = 'etlap';
  break;
  case 'terkep':
    $vw['title'] = 'Lelle Étterem és Pizzéria - Balatonlelle - Hol találsz minket?';
    $vw['description'] = 'Lelle Étterem és Pizzéria - Balatonlelle. Itt az igazi nápolyi pizza!';
    $vw['page'] = 'terkep';
  break;
  default:
    $vw['title'] = 'Lelle Étterem és Pizzéria - Balatonlelle';
    $vw['description'] = 'Lelle Étterem és Pizzéria - Balatonlelle. Az igazi nápolyi pizza';
    $vw['page'] = 'home';
  break;
}

header( 'Access-Control-Allow-Origin: *' );
require_once 'view_html.php';