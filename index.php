<?php
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);
session_start();

$url = ( strpos( $_SERVER['REQUEST_URI'], 'index.php' ) !== false )
       ? substr( $_SERVER['REQUEST_URI'], 0, strpos( $_SERVER['REQUEST_URI'], 'index.php' ))
       : $_SERVER['REQUEST_URI'];
$url = ( $url[0] == '/' )? (( strlen( $url ) > 1 )? substr( $url, 1 ) : '' ) : $url;
$url = ( strpos( $url, '?' ) !== false )? substr( $url, 0, strpos( $url, '?' )) : $url;
$url = ( substr( $url, -1 ) == '/' )? substr( $url, 0, -1 ) : $url;
$url_request = '/'.$url;
$url = explode( '/', $url );
foreach( $url as $k => $e ) if( strlen( $e ) == 0 ) unset( $url[$k] );
$route = ( count( $url ))? $url : ['home'];
if( $route[0] == 'logout' ){
  unset( $_SESSION['admin'] );
  $route = ['home'];
}
if( $route[0] == 'login' and !isset( $_SESSION['admin'] ) and isset( $_POST ) and isset( $_POST['psw'] )){
  $_POST['psw'] = preg_replace( '/[^a-zA-Z0-9_]/', '', $_POST['psw'] );
  $_POST['psw'] = trim( strip_tags( $_POST['psw'] ));
  if( $_POST['psw'] == date( 'y' ) . 'Le' . date( 'md' )){
    $_SESSION['admin'] = true;
    $route = ['admin'];
  }
}

$vw['config'] = json_decode( file_get_contents( 'upload/config.json' ));
if( !in_array( $route[0], ['home', 'galeria', 'etlap', 'terkep', 'login', 'admin', 'telefon', 'pdf', 'kepek'] )) $route = ['home'];
$vw['page'] = $route[0];
switch( $route[0] ){
  case 'login':
    $vw['title'] = 'Lelle Étterem és Pizzéria - Balatonlelle - Bejelentkezés';
    $vw['description'] = 'Lelle Étterem és Pizzéria - Balatonlelle. Bejelentkezés az oldalra.';
  break;
  case 'telefon':
    if( !( $vw['config'] ?? 0 ))
      $vw['config'] = (object)[];
    if( !( $vw['config']->telefon ?? 0 ))
      $vw['config']->telefon = '';
    if( isset( $_POST['ment'] ) and isset( $_POST['telefon'] )){
      $_POST['telefon'] = preg_replace( '/[^0-9+\-]/', '', $_POST['telefon'] );
      $_POST['telefon'] = trim( strip_tags( $_POST['telefon'] ));
      $vw['config']->telefon = $_POST['telefon'];
      file_put_contents( 'upload/config.json', json_encode( $vw['config'] ));
    }
    $_POST['telefon'] = $vw['config']->telefon ?? '';
  break;
  case 'pdf':
    $_POST['ok'] = false;
    if( isset( $_POST['btn_ment'] ) and isset( $_FILES['pdf'] ) and $_FILES['pdf']['error'] == 0 )
      if( move_uploaded_file( $_FILES['pdf']['tmp_name'], 'upload/etlap.pdf' ))
        $_POST['ok'] = true;
  break;
  case 'kepek':
    $_POST['ok'] = false;
    if( isset( $_POST['btn_slides'] ) and isset( $_POST['slides'] )){
      $slides = explode( ',', $_POST['slides'] );
      foreach( $slides as $k => $slide )
        if( !file_exists( 'upload/le'.$slide.'.jpg' ))
          unset( $slides[$k] );
      file_put_contents( 'upload/slides.json', json_encode( $slides ));
      $_POST['ok'] = 'Lapozó mentve';
    }

    if( isset( $_POST['btn_newgallery'] ) and isset( $_POST['newgallery'] )){
      $galleries = json_decode( file_get_contents( 'upload/galleries.json' )) ?? (object)[];
      $newid = 0;
      foreach( $galleries as $gallery ) if( $newkey < $gallery->id ) $newid = $gallery->id;
      $newid++;
      $newgallery = (object)[
        'id' => $newid,
        'name' => $_POST['newgallery'],
        'images' => []
      ];
      $galleries[] = $newgallery;
      file_put_contents( 'upload/galleries.json', json_encode( $galleries ));
      $_POST['ok'] = 'Új galéria létrehozva';
    }

    if( isset( $route[1] ) and $route[1] == 'gdel' ){
      $galleries = json_decode( file_get_contents( 'upload/galleries.json' )) ?? (object)[];
      foreach( $galleries as $k => $gallery )
        if( $gallery->id == $route[2] )
          unset( $galleries[$k] );
      file_put_contents( 'upload/galleries.json', json_encode( $galleries ));
      $_POST['ok'] = 'Galéria törölve';
    }

    if( isset( $_POST['btn_gallery'] )){
      $galleries = json_decode( file_get_contents( 'upload/galleries.json' )) ?? (object)[];
      foreach( $_POST as $key => $post )
        if( substr( $key, 0, 8 ) == 'gallery_' ){
          $gallery_id = substr( $key, 8 );
          $images = explode( ',', $post );
          foreach( $images as $k => $image )
            if( !file_exists( 'upload/le'.$image.'.jpg' ))
              unset( $images[$k] );
          foreach( $galleries as $key2 => $gallery )
            if( $gallery->id == $gallery_id )
              $galleries[$key2]->images = $images;
        }
      file_put_contents( 'upload/galleries.json', json_encode( $galleries ));
      $_POST['ok'] = 'Galéria módosítva';
    }

    if( isset( $route[1] ) and $route[1] == 'kdel' ){
      unset( 'upload/le' . $route[2] . '.jpg' );
      $_POST['ok'] = 'Kép törölve';
    }

    if( isset( $_POST['btn_ment'] ) and isset( $_FILES['pdf'] ) and $_FILES['pdf']['error'] == 0 )
      if( move_uploaded_file( $_FILES['pdf']['tmp_name'], 'upload/etlap.pdf' ))
        $_POST['ok'] = true;

    $vw['slides'] = json_decode( file_get_contents( 'upload/slides.json' )) ?? (object)[];
    $vw['galleries'] = json_decode( file_get_contents( 'upload/galleries.json' )) ?? (object)[];
    $vw['images'] = glob( 'upload/le*.{jpg,jpeg}', GLOB_BRACE );
  break;
  case 'galeria':
    $vw['title'] = 'Lelle Étterem és Pizzéria - Balatonlelle - Galéria';
    $vw['description'] = 'Lelle Étterem és Pizzéria - Balatonlelle. Fotógaléria.';
    $vw['galleries'] = json_decode( file_get_contents( 'upload/galleries.json' ));
    if( isset( $route[1] )){
      foreach ( $vw['galleries'] as $gallery )
        if( $gallery->id == $route[1] )
          foreach( $gallery->images as $kep )
            $vw['images'][] = '/upload/le'.$kep.'.jpg';
    }else
      $vw['images'] = glob( 'upload/le*.{jpg,jpeg}', GLOB_BRACE );
  break;
  case 'etlap':
    $vw['title'] = 'Lelle Étterem és Pizzéria - Balatonlelle - Étlap';
    $vw['description'] = 'Lelle Étterem és Pizzéria - Balatonlelle. Étlapon a teljes választékunk.';
  break;
  case 'terkep':
    $vw['title'] = 'Lelle Étterem és Pizzéria - Balatonlelle - Hol találsz minket?';
    $vw['description'] = 'Lelle Étterem és Pizzéria - Balatonlelle. Itt az igazi nápolyi pizza!';
  break;
  default:
    $vw['title'] = 'Lelle Étterem és Pizzéria - Balatonlelle';
    $vw['description'] = 'Lelle Étterem és Pizzéria - Balatonlelle. Az igazi nápolyi pizza';
    $vw['slides'] = json_decode( file_get_contents( 'upload/slides.json' ));
  break;
}

header( 'Access-Control-Allow-Origin: *' );
if( $route[0] == 'login' or ( in_array( $route[0], ['admin', 'telefon', 'pdf', 'kepek'] ) and isset( $_SESSION['admin'] )))
  require_once 'view_htmladmin.php';
else
  require_once 'view_html.php';