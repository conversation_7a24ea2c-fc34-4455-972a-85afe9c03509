@charset "UTF-8";
/* Layout - minden oldara kiterjedő */

body{
  /*overflow-x: hidden;*/
  background-color: var( --bg-color );
  color: var( --color );
  div.fixed-btn{
    position: fixed;
    z-index: 1;
    top: 92px;
    right: 0;
    & > a{
      border-top-left-radius: .5rem;
      border-bottom-left-radius: .5rem;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.5rem 1rem;
      border: 1px solid transparent;
      font-size: 1rem;
      background-color: var(--btn-bg-color);
      color: var(--btn-color) !important;
    }
    &.advertall-btn{
      top: 140px;
      & > a{ background-color: var(--reserved-color) }
    }
  }
  & main{
    container: main-size / inline-size;
    & > header{
      & > div{
        display: flex;
        flex-flow: row wrap;
        justify-content: space-around;
        align-items: center;
        background-color: var(--header-bg-color);
        & > ul{
          display: flex;
          flex-flow: row wrap;
          justify-content: space-around;
          align-items: center;
          & > li{
            padding: 4px 8px;
            &::before{ margin-right: 8px }
            & > details{
              display: none;
              & > summary{
                cursor: pointer;
                list-style: none;
                font-size: 2em;
              }
              & > div{
                position: absolute;
                left: calc(50% - 160px);
                z-index: 1;
                padding: 4px;
                background-color: var(--header-bg-color);
              }
            }
          }
        }
      }
      & > nav{
        width: 100%;
        padding: 0 20px;
        & .nav_content{
          height: 100%;
          margin: 0 100px 0 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          & .logo{
            min-width: 128px;
            & > a{
              font-size: 35px;
              font-weight: 500;
              color: var(--color);
              & img{ height: 160px }
            }
          }
          & .nav_links{
            display: flex;
            flex-flow: row wrap;
            align-items: center;
            & > li{
              list-style: none;
              margin: 0 8px;
              & > a{
                display: inline-block;
                font-size: 1.1rem;
                font-weight: 500;
                color: var(--color);
                padding: 10px 15px; 
                transition: all 0.3s ease;
              }
            }
          }
        }
      }     
    }
    & > footer{
      padding: 3rem 1rem;
      background-color: var(--header-bg-color);
      & > ul{
        display: flex;
        flex-flow: row wrap;
        justify-content: center;
        & > li{
          padding: 0 1rem;
          & > a{ text-transform: uppercase }
        }
      }
    }
  }
}

@container main-size (max-width: 480px ){
  header > nav{ display: none !important }
  header details{ display: block !important }
}
@container main-size ( max-width: 600px ){
  section { flex-grow: 1 }
}

body#home{
  & dialog.advertall{
    position: relative;
    top: 50%;
    transform: translate(0, -50%);
    & div{
      display: flex;
      justify-content: center;
      position: absolute;
      bottom: 2px;
      width: 100%;
    }
    & a{
      &:not(.btn){
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 2px;
        right: 2px;
        width: 1.5rem;
        height: 1.5rem;
        padding: 2px;
        border-radius: 50%;
        color: #000 !important;
        background-color: #fff;
      }
    }
  }
  & input[type="submit"]{ pointer-events: none }
  & li:has(#gdpr:checked) + li > input{ pointer-events: auto }
  & main{
    & > .wrapper{
      display: flex;
      flex-flow: row wrap;
      position: relative;
      &>video{
        position: relative;
        width: 100%;
      }
      & > div.hero{
        position: absolute;
        top: 5vw;
        left: 5%;
        padding: 2rem 1rem;
        opacity: .7;
        background-color: var(--color);
        color: var(--bg-color);
        font-family: Avenir;
        font-size: 2vw;
        & > h1{
          text-align: center;
          color: var(--work-medium);
          border-bottom: 8px solid var(--btn-bg-color);
          margin-bottom: 2rem;
        }
      }
      & > div#szolg,
      & > div#ahaz{
        width: 100%;
        padding: 4rem;
        & > h2{
          text-align: center;
          margin-bottom: 4rem;
        }
        & > dl{
          display: flex;
          flex-flow: row wrap;
          justify-content: space-around;
          align-items: stretch;
          padding: 0 4rem;
          & dt{ width: clamp(320px, 45%, 600px) }
          & dd{
            flex: 1 1 320px;
            padding: 0 0 0 4vw;
            & > p{
              margin: 1rem 0;
              font-family: Avenir;
              font-size: 1.2rem;
            }
          }
        }
      }
      & > div#szolg{
        & > dl > dd{ padding: 0 4vw 0 0 }
        & > ul{
          display: flex;
          flex-flow: row wrap;
          justify-content: space-between;
          width: 100%;
          & > li{
            width: 22%;
            padding: 1rem;
            text-align: center;
            & > img{ width: 100% }
            & > i::before{
              font-size: 4rem;
              color: var(--btn-bg-color);
            }
            & > h3{ margin-top: 1rem }
          }
        }
      }
      & > div#galeria{
        width: 100%;
        padding: 4rem;
        text-align: center;
        & > h2{
          text-align: center;
          margin-bottom: 4rem;
        }
        & > div{
          display: flex;
          flex-flow: row wrap;
          justify-content: space-between;
          width: 100%;
          & > img{
            width: 25%;
            padding: .2rem;
            text-align: center;
          }
        }
        & > a{ margin-top: 4rem }
      }
      & > div.velemeny{
        width: 100%;
        padding: 4rem;
        text-align: center;
        & > h2{
          text-align: center;
          margin-bottom: 1rem;
        }
        & > div{
          margin-bottom: 4rem;
          &.step{
            margin: 0;
            font-size: 2rem;
            & > span{ cursor: pointer }
          }
        }
        & > section{
          display: flex;
          max-width: 100%;
          min-width: 320px;
          padding: 0 20px 20px;
          gap: 0.1rem 0;
          overflow-x: auto;
          scroll-snap-type: inline mandatory;
          & > figure{
            flex: 0 0 clamp(320px, 44vw, 600px);
            scroll-snap-align: center;
            padding: 0 2rem;
            text-align: center;
            & > p{
              text-align: left;
              font-family: Avenir;
            }
            & > a{
              display: inline-block;
              margin-top: 2rem;
              font-size: .8em;
              text-transform: uppercase;
            }
          }
          &:focus-visible{ outline: 0 }
        }
      }
      & > div#map{
        width: 100%;
        padding: 4rem;
        text-align: center;
        & > h2{
          text-align: center;
          margin-bottom: 4rem;
        }
        & > #map-container{
          margin-bottom: 4rem;
          width: 100%;
          height: 350px;
        }
      }
      & > div.hirlevel{
        width: 100%;
        padding: 4rem;
        text-align: center;
        background: transparent url(https://tren.hu/upload/84/pic0033.jpg) center no-repeat;
        background-size: cover;
        & > div{
          z-index: 2;
          position: relative;
          width: 100%;
          max-width: 600px;
          margin: auto;
          padding: 2rem;
          background-color: transparent;
          color: var(--bg-color);
          font-family: Avenir;
          & > i{
            display: inline-block;
            font-size: .8rem;
            padding-bottom: 1rem;
          }
          & > p{ font-size: 1.6rem }
          & > form{
            & > ul{
              & > li{
                & > input{
                  &[type="submit"]{ width: 100% !important }
                }
                & > label{
                  line-height: 1rem !important;
                  text-align: left;
                  height: 2rem !important;
                  & > a{ color: var(--btn-bg-color) !important }
                  &::before{
                    width: 2rem !important;
                    height: 2rem !important;
                    font-size: 1.5rem !important;
                  }
                }
              }
            }
          }
          & > div.opacitybox{
            z-index: -1;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: .7;
            background-color: var(--color);
          }
        }
      }
    }
  }
}

@container (width < 601px){
  body#home main > .wrapper{
    & > div.hero{ top: .5% }
    & > div.velemeny{ padding: 2rem }
    & > div#ahaz,
    & > div#szolg,
    & > div#galeria{
      padding: 2rem;
      & > ul > li{
        width: 50%
      }
    }
    & > div#ahaz > dl,
    & > div#szolg > dl{
      padding: 0;
      & >dd { padding: 0 }
    }
    & > div#map{ padding: 2rem }
    & > div.hirlevel{ padding: 2rem }
  }
  body#booking main > .wrapper{
    & > div{ padding: 2rem 0 !important }
  }
}

@container (width < 361px){
  .nav_links > li > a{ font-size: 3.8vw !important }

  body#home main > .wrapper{
    & > dl{ padding: 1% }
    & > div.velemeny{ padding: 1% }
    & > div#szolg{ padding: 1% }
    & > div#galeria{ padding: 1% }
    & > div#map{ padding: 1% }
    & > div.hirlevel{ padding: 1% }
  }
}
 
body#booking{
  & > div.fixed-btn{ display: none }
  & main{
    & > .wrapper{
      display: flex;
      flex-flow: row wrap;
      position: relative;
      & > div{
        width: 100%;
        padding: 4rem;
        & > h1{
          text-align: center;
          margin-bottom: 4rem;
        }
        & > h2{ margin: 1rem 0 }
        & > p{ margin-bottom: .5rem }
        & > table{
          margin: .5rem 0 !important;
          width: fit-content !important;
          background-color: inherit !important;
          color: inherit !important;
          & th{
            background-color: inherit !important;
            color: inherit !important;
          }
          & td{ height: max(3rem, 48px) !important }
        }
        & > form{
          display: flex;
          flex-flow: row wrap;
          justify-content: center;
          width: 100%;
          max-width: 620px;
          margin: 0 auto;
          & > ul{
            width: 100%;
            & > li{
              width: 100%;
              & > ul{
                & > li{
                  & button#save{ pointer-events: none }
                  &:has(#aszf:checked) + li > button#save{ pointer-events: auto }
                  & > button{ width: 100% }
                  & > ul{
                    display: grid;
                    grid-template-columns: repeat(7, 1fr);
                    grid-row-gap: .2rem;
                    min-width: 294px;
                    max-width: 320px;
                    & > li{
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      height: 2.5rem;
                      user-select: none;
                      border: solid 2px transparent;
                      &.holiday{
                        font-weight: bold;
                        color: var(--holiday-color, inherit);
                      }
                      &[data-day]{
                        align-items: start;
                        flex-flow: row wrap;
                        max-width: 2.5rem;
                        & > label{
                          padding: 0 !important;
                          margin: 0 !important;
                          width: 2rem !important;
                          height: 2rem !important;
                          line-height: 2rem !important;
                          &::after{
                            width: 2rem !important;
                            height: 2rem !important;
                            padding: 0 !important;
                          }
                        }
                        &.selected,
                        &.start,
                        &.stop{
                          height: 4rem;
                          background-color: var(--select-color, #c99616);
                        }
                        &:hover{ border: solid 2px var(--select-color, #c99616) }
                        &.start{ border-radius: 1rem 0 0 1rem }
                        &.stop{ border-radius: 0 1rem 1rem 0 }
                        &.start.stop{ border-radius: 1rem } 
                      }
                    }
                  }
                  &.extra{
                    padding: .5rem 0;
                    background-color: var(--header-bg-color);
                    & > h3{
                      text-align: center;
                      padding: 1rem 0;
                    }
                    & > span{
                      display: block;
                      padding: 1rem 0;
                      margin: .5rem 0;
                      border-top: 1px solid;
                      border-bottom: 1px solid;
                    }
                    & > p{
                      padding-bottom: .5rem;
                      margin-bottom: .5rem;
                      border-bottom: 1px solid;
                    }
                  }
                  & > i.btn{
                    width: 100%;
                    &::before{ font-size: 2rem }
                  }
                  & > label[for="aszf"]{
                    height: 2rem !important;
                    line-height: 1rem !important;
                    &::before{
                      color: #000 !important;
                      width: 2rem !important;
                      height: 2rem !important;
                      font-size: 1.5rem !important;
                    }
                  }
                }
              }
              &[data-extra]{
                & > ul:first-child:not(ul:last-child){ margin-top: .5rem }
                & > ul:last-child:not(ul:first-child){ margin-top: .5rem }
              }
            }
          }
          & .error{ border: 3px solid red !important }
        }
      }
    }
  }
}

@container (width < 601px){
  body#booking main > .wrapper{
    
  }
}

@container (width < 361px){
  .nav_links > li > a{ font-size: 3.8vw !important }

  body#booking main > .wrapper{
    
  }
}

body#galeria{
  & main{
    & > .wrapper{
      display: flex;
      flex-flow: row wrap;
      position: relative;
        & > h1{
          width: 100%;
          text-align: center;
          margin-bottom: 4rem;
        }
        & > ul{
          display: flex;
          flex-flow: row wrap;
          justify-content: center;
          gap: 2rem;
          min-width: 320px;
          width: 100%;
          padding: 0 4rem 4rem;
        }
        & > div{
          display: flex;
          flex-flow: row wrap;
          justify-content: center;
          align-items: start;
          min-width: 320px;
          width: 100%;
          padding: 0 4rem 4rem;
          & > img{
            height: 100%;
            max-height: 250px;
            transition: 1s ease;
            &:hover{
              -webkit-transform: scale(1.2);
              -ms-transform: scale(1.2);
              transform: scale(1.2);
              transition: 1s ease;
            }
          }
        }
    }
  }
}

@container (width < 481px){
  body#galeria main > .wrapper > div > img{
    height: auto;
    max-height: none;
  }
}

body#elmenytar{
  & main{
    & > .wrapper{
      display: flex;
      flex-flow: row wrap;
      position: relative;
      & > h1{
        width: 100%;
        text-align: center;
      }
      & > article{
        min-width: 320px;
        width: 100%;
        padding: 4rem;
      }
      & > form{
        display: flex;
        justify-content: center;
        min-width: 320px;
        width: 100%;
        padding: 0 4rem 4rem;
      }
      & > ul{
        display: flex;
        flex-flow: row wrap;
        justify-content: center;
        gap: 2rem;
        min-width: 320px;
        width: 100%;
        padding: 0 4rem 4rem;
      }
    }
  }
}

body#szepkartya{
  & main{
    & > .wrapper{
      display: flex;
      flex-flow: row wrap;
      position: relative;
      & > h1{
        width: 100%;
        text-align: center;
      }
      & > article{
        min-width: 320px;
        width: 100%;
        padding: 4rem;
        & > ul{
          margin: 2rem;
          & > li{ list-style: disc !important }
        }
        & > p{
          & > b{
            display: inline-block;
            margin: 2rem 0;
          }
          & > a{ margin-top: 1rem }
        }
      }
    }
  }
}

