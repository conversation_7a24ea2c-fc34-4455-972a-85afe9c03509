@charset "UTF-8";

::before, ::after, *{
  box-sizing: border-box;
  text-rendering: optimizeLegibility;
}
*{
  margin: 0;
  padding: 0;
}
:root{ isolation: isolate }
body{
  min-height: 100vh;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}
img, picture, video, canvas, svg{
  display: block;
  max-width: 100%;
}
img, fieldset{ border: none }
p, h1, h2, h3, h4, h5, h6{ overflow-wrap: break-word }
input, button, textarea, select{ font: inherit }
button,
input[type=button],
input[type=reset],
input[type=submit],
input[type=checkbox],
input[type=radio],
select{ cursor: pointer }
button[disabled],
input[disabled]{ cursor: default }

@font-face{
  font-family: "Avenir";
  src: url("Avenir.ttc") format("truetype");
}
@font-face{
  font-family: "Fashion Fetish";
  src: url("Fashion Fetish Small Caps.ttf") format("truetype");
}

::-webkit-scrollbar{
  width: .5rem;
  height: .5rem;
}
::-webkit-scrollbar-track{
  background-color: var(--bg-color);
  border-radius: .5rem;
}
::-webkit-scrollbar-thumb{
  background: var(--btn-bg-color);
  border-radius: .5rem;
}
::-webkit-scrollbar-button{ display: none }

[style^="--icon:"]:before,
[style*=" --icon:"]:before{
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: FontIcon;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  content: var(--icon);
}

li{ list-style: none }

a, object,
input:focus,
textarea:focus,
button:focus{ outline: none !important }

a{
  text-decoration: none;
  cursor: pointer;
  color: var(--link-color);
  &:hover{ text-decoration: underline }
}

body{
  font-family: "Fashion Fetish";
}

:root {
  --primary: #aa825d;
  --secondary: #ffd700;
  --secondary: #1c4620;
  --work-light: #ececec;
  --work-medium: #888888;
  --work-dark: #232323;
  --dark: #1c1c1c;
  --light: #fff;
        
  --danger-color: #ff8569;
  --warning-color: #fcc74f;
  --success-color: #d9ed92;
  --info-color: #97e2f7;

  --color: var(--light);
  --bg-color: var(--work-dark);
  --btn-color: var(--light);
  --btn-bg-color: var(--secondary);
  --select-color: var(--secondary);
  --reserved-color: var(--danger-color);
  --form-bg-color: var(--light);
  --form-border-color: transparent;
  --form-focus-color: var(--work-dark);
  --form-focus-bg-color: var(--work-light);
  --form-focus-border-color: var(--work-dark);
  --form-select-bg-color: var(--work-medium);
  --form-label-color: var(--secondary);
  --sample-disabled: var(--work-light);
  --profirat-color: var(--light);
  --profirat-bg-color: var(--work-medium);
  --profirat-border-color: var(--work-light);
  --profirat-link-color: var(--warning-color);
  --profirat-link-hover-color: var(--danger-color);
  --profirat-link-bg-color: var(--secondary);

  --header-bg-color: var(--dark);
  --link-color: var(--light);
  --link-hover-color: var(--dark);
  --btn-hover-color: var(--light);
  --btn-hover-bg-color: var(--tertiary);
  --dashboard-color: var(--dark);
  --dashboard-bg-color: var(--secondary);
  --dashboard-border-color: var(--work-lighter);
  --dashboard-hover-color: var(--dark);
  --dashboard-hover-bg-color: var(--work-medium);
  --dashboard-sec-color: var(--dark);
  --dashboard-sec-bg-color: var(--primary);
  --dashboard-sec-hover-color: var(--dark);
  --dashboard-sec-hover-bg-color: var(--work-medium);
  --dashboard-active-color: var(--dark);
  --dashboard-active-bg-color: var(--light);
  --dashboard-active-border-color: var(--warning-color);
  --cart-color: var(--dark);
  --cart-bg-color: var(--work-dark);
  --cart-border-color: var(--primary);
  --cart-article-bg-color: var(--light);
  --table-dt-bg-color: var(--light);
  --table-color: var(--dark);
  --table-bg-color: var(--light);
  --table-border-color: var(--work-medium);
  --table-thead-color: var(--setcolor3);
  --table-thead-bg-color: var(--light);
  --table-tfoot-color: var(--setcolor3);
  --table-tfoot-bg-color: var(--setcolor0);
  
  --icon-phone: '\e807';
  --icon-mail-alt: '\f0e0';
  --icon-location: '\e806';
  --icon-facebook: '\f09a';
  --icon-instagram: '\f16d';
  --icon-twitter: '\f099';
  --icon-bicycle: '\f206';
  --icon-television: '\f26c';
  --icon-star: '\e826';
  --icon-menu: '\f0c9';

  --icon-key: '\e80a';
  --icon-ok: '\e80b';
  --icon-gauge: '\f0e4';
  --icon-calendar: '\e808';
  --icon-money: '\f0d6';
  --icon-exchange: '\f0ec';
  --icon-bed: '\f236';
  --icon-cog: '\e817';
  --icon-left-open: '\e824';
  --icon-right-open: '\e825';
  --icon-edit: '\e804';
  --icon-picture: '\e805';
  --icon-trash-empty: '\e816';
  --icon-sort-down: '\f0dd';
  --icon-sort-up: '\f0de';
  --icon-cancel-circled: '\e80c';
  --icon-plus-circled: '\e80d';
  --icon-cancel: "\e823";
  --icon-resize-full: '\e81a';
  --icon-resize-small: '\e81b';
  --icon-upload: '\e81e';
  --icon-arrows-cw: '\e822';
  --icon-blocked: '\e82a';
  --icon-plus: '\e82c';
  --icon-angle-double-down: '\f103';
  --icon-spinner: "\f110";
}

@font-face{
  font-family: "FontIcon";
  font-display: fallback;
  src: url("fonticon.eot?") format("eot"), url("fonticon.woff2") format("woff2"), url("fonticon.woff") format("woff"), url("fonticon.ttf") format("truetype"), url("fonticon.svg#FontIcon") format("svg");
}

body{
  background-color: var( --bg-color );
  color: var( --color );
  & main{
    container: main-size / inline-size;
    & > header{
      & > div{
        display: flex;
        flex-flow: row wrap;
        justify-content: space-around;
        align-items: center;
        background-color: var(--header-bg-color);
        & > ul{
          display: flex;
          flex-flow: row wrap;
          justify-content: space-around;
          align-items: center;
          & > li{
            padding: 4px 8px;
            &::before{ margin-right: 8px }
            & > details{
              display: none;
              & > summary{
                cursor: pointer;
                list-style: none;
                font-size: 2em;
              }
              & > div{
                position: absolute;
                left: calc(50% - 160px);
                z-index: 1;
                padding: 4px;
                background-color: var(--header-bg-color);
              }
            }
          }
        }
      }
      & > nav{
        width: 100%;
        padding: 0 20px;
        & .nav_content{
          height: 100%;
          margin: 0 100px 0 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          & .logo{
            min-width: 128px;
            & > a{
              font-size: 35px;
              font-weight: 500;
              color: var(--color);
              & img{ height: 160px }
            }
          }
          & .nav_links{
            display: flex;
            flex-flow: row wrap;
            align-items: center;
            & > li{
              list-style: none;
              margin: 0 8px;
              & > a{
                display: inline-block;
                font-size: 1.1rem;
                font-weight: 500;
                color: var(--color);
                padding: 10px 15px; 
                transition: all 0.3s ease;
              }
            }
          }
        }
      }     
    }
  }
}

@container main-size (max-width: 480px ){
  header > nav{ display: none !important }
  header details{ display: block !important }
}

body#terkep{
  & main{
    & > .wrapper{
      display: flex;
      flex-flow: row wrap;
      position: relative;
      & > div#map{
        width: 100%;
        text-align: center;
        & > h2{
          text-align: center;
          margin-bottom: 1rem;
        }
        & > #map-container{
          margin-bottom: 4rem;
          width: 100%;
          height: 400px;
        }
      }
    }
  }
}

/* CSS-alapú slider stílusok */
.slider-container {
  position: relative;
  width: 100vw;
  height: 60vh;
  margin-left: calc(-50vw + 50%);
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.slider-container input[type="radio"] {
  display: none;
}

.slider-wrapper {
  display: flex;
  width: 400%;
  height: 100%;
  transition: transform 0.6s ease-in-out;
  animation: autoSlide 12s infinite;
}

.slide {
  width: 25%;
  height: 100%;
  position: relative;
}

.slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Slider navigáció */
.slider-nav {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 10;
}

.slider-nav label {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.slider-nav label:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
}

/* Slider pozíciók - csak akkor aktív, ha nincs animáció */
#slide1:checked ~ .slider-wrapper {
  transform: translateX(0%) !important;
  animation: none !important;
}

#slide2:checked ~ .slider-wrapper {
  transform: translateX(-25%) !important;
  animation: none !important;
}

#slide3:checked ~ .slider-wrapper {
  transform: translateX(-50%) !important;
  animation: none !important;
}

#slide4:checked ~ .slider-wrapper {
  transform: translateX(-75%) !important;
  animation: none !important;
}

/* Aktív navigációs pont */
#slide1:checked ~ .slider-nav label:nth-child(1),
#slide2:checked ~ .slider-nav label:nth-child(2),
#slide3:checked ~ .slider-nav label:nth-child(3),
#slide4:checked ~ .slider-nav label:nth-child(4) {
  background-color: var(--secondary);
  border-color: var(--secondary);
}

/* Automatikus slider animáció - 3 másodpercenként */
@keyframes autoSlide {
  0%, 22% { transform: translateX(0%); }
  25%, 47% { transform: translateX(-25%); }
  50%, 72% { transform: translateX(-50%); }
  75%, 97% { transform: translateX(-75%); }
  100% { transform: translateX(0%); }
}

/* Automatikus lapozás aktiválása már a fenti .slider-wrapper szabályban */

/* Hover esetén az animáció szüneteltetése */
.slider-container:hover .slider-wrapper {
  animation-play-state: paused;
}

/* PDF megjelenítés stílusai */
.pdf-container {
  width: 100%;
  margin: 2rem 0;
  border: 1px solid var(--work-medium);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pdf-container embed {
  display: block;
  width: 100%;
  min-height: 600px;
  border: none;
}

.pdf-container p {
  text-align: center;
  padding: 2rem;
  color: var(--color);
  font-size: 1.1rem;
}

.pdf-container a {
  color: var(--secondary);
  font-weight: bold;
  text-decoration: underline;
}

.pdf-container a:hover {
  color: var(--primary);
}

/* Responsív design */
@media (max-width: 768px) {
  .slider-container {
    height: 40vh;
  }

  .slider-nav label {
    width: 12px;
    height: 12px;
  }

  .pdf-container embed {
    min-height: 500px;
  }
}

@media (max-width: 480px) {
  .pdf-container embed {
    min-height: 400px;
  }
}