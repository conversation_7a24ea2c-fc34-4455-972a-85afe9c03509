import { $ } from '../default.js';

export function ajax( param ){
  const xhttp = new XMLHttpRequest() || false
  if( !xhttp ) return false
  xhttp.open( 
    param.method || 'POST',
    param.url,
    ( typeof param.async !== 'undefined' )? param.async : true,
    param.user || null,
    param.password || null
  )
  if( param.reqHeader )
    xhttp.setRequestHeader( 'Content-Type', param.reqHeader )
  xhttp.send( param.body || null )
  xhttp.onreadystatechange = (e) => {
    if( e.target.readyState === 4 )
      if( e.target.status === 200 )
        if( param.done )
          param.done( e.target )
        else
          console.log( 'NOT DONE', e.target )
      else
        if( param.fail )
          param.fail( { code: e.target.status, message: e.target.statusText } )
  }
}

export function loadHTML2render( param = null ){
  async function fetchHTML(){
    let container = typeof param.html === 'object' ? param.html : $( param.html )
    try{
      const
        response = await fetch( param.loadFile ),
        html = await response.text()
      container.innerHTML = html
      if( param.afterBuilding ) param.afterBuilding()
      return true
    }catch( error ){
      container.innerHTML = 'Sikertelen művelet!'
      console.log( error )
      return false
    }
  }
  return fetchHTML()
}

export function apiJSON( param = {} ){
  async function fetchAPI( param ){
    try{
      let options = {
        method: param.method || 'POST',
        headers: param.headers || {},
        body: param.body
      }
      const
        response = await fetch( param.url, options ),
        data = await response.json()
      return { status: response.status, data }
    }catch( error ){
      console.log( error )
      return { status: 500, error: error.message || 'Internal Server Error' }
    }
  }
  return fetchAPI( param )
}

/*
export function apiHTML2render( param = null ){
  async function getHTML( param ){
    const
      response = await apiJSON( {
        url: param.url,
        body: param.body,
        headers: {'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8'}
      } )
    if( response.status >= 200 && response.status < 300 ){
      for (const [html, contener] of Object.entries( param.html ) )
        if( response.data[html] )
          contener.innerHTML = response.data[html]
      if( param.afterBuilding ) param.afterBuilding()
    }
    return response
  }
  return getHTML( param )
}
*/
export function apiHTML2render( param = null ){
  ajax({
    url: param.url,
    reqHeader: 'application/x-www-form-urlencoded;charset=UTF-8',
    body: param.body,
    done: ( back ) => {
      let response = JSON.parse( back.response )
      for (const [html, contener] of Object.entries( param.html ) )
        if( response[html] && contener )
          contener.innerHTML = response[html]
      if( param.afterBuilding ) param.afterBuilding()
    }
  })
}