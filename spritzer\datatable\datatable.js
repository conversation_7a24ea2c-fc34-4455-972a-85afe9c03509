import { $, $$ } from '../default.js';
import { apiHTML2render, ajax } from '../asynccom/asynccom.js';
import { dialog } from '../dialog/dialog.js';

export function datatable( param = {} ){
  const datatables = $$( '[data-datatable]' )
  datatables ? datatables.forEach( datable => {
    datable.addEventListener( 'click', eventManager )
    datable.addEventListener( 'change', eventManager )
    param.events ? param.events.forEach( eventType => {
      if( eventType != 'change' && eventType != 'click' )
        datable.addEventListener( eventType, eventManager )
    } ) : null
    param.create ? datable.closest( '.cart' ).querySelector( '.btn.create' ).addEventListener( 'click', eventManager ) : null
  } ) : null

  function setHeadOrder( datable, order ){
    let sort = order.getAttribute( 'style' )
    const iicons = $$( 'th[data-order]>i', datable )
    iicons.forEach( iicon => {
      iicon.setAttribute( 'style', '--icon:var(--icon-sort-1)' )
    } )
    if( sort == '--icon:var(--icon-sort-1)' )
      sort = '--icon:var(--icon-sort-down)'
    else if( sort == '--icon:var(--icon-sort-down)' )
      sort = '--icon:var(--icon-sort-up)'
    else
      sort = '--icon:var(--icon-sort-down)'
    order.setAttribute( 'style', sort )
  }

  function makePagination( datable, page = null ){
    loadHTML2render( {
      loadFile: 'ajax_table.php',
      html: '[data-datatable="'+ datable.dataset.datatable +'"] .pagination',
      body: 'function=makePagination&datatable='+ datable.dataset.datatable + ( page ? '&page='+ page : '' )
    } )
  }

  function eventManager( event ){
    const datable = event.target.closest( '[data-datatable]' ) || $( '[data-datatable]', event.target.closest( '.cart' ) )
    let
      activePage = $( '.pagination .active', datable ) ? parseInt( $( '.pagination .active', datable ).innerHTML ) : 1,
      limit = $( 'select[name="limit"]', datable ) ? parseInt( $( 'select[name="limit"]', datable ).value ) : 10,
      order = $( 'thead[data-order]', datable ) ? $( 'thead[data-order]', datable ).dataset.order : null,
      where = JSON.parse( datable.dataset.where || '{}' ),
      eventElement = null,
      fields = ''

    const selector = getEventFunction()
    if( selector )
      switch( selector ){
        case '.btn.import':
          param.eventManagerFunctions( ['', datable.dataset.datatable + '_import', null] )
        break
        case '.btn.create':
          if( param?.create )
            if( param.create.loadFile ){
              dialog( {
                type: 'modal',
                id: datable.dataset.datatable +'-create',
                isCloseBack: true,
                isKeepOpen: true,
                loadFile: param.create.loadFile,
                eventManagers: {
                  'callback': ['', datable.dataset.datatable + '_create']
                },
                eventManagerFunctions: param.eventManagerFunctions
              } )
            }else{
              if( param?.create?.fields )
                fields = param.create.fields.map( field => {
                  return Object.entries( field ).map(( [key, value] ) => {
                    return '<input type="hidden" name="'+ key +'" value="'+ value +'">'
                  } ).join( '' )
                } ).join( '' )
              dialog( {
                type: 'modal',
                id: datable.dataset.datatable +'-create',
                is_ko: true,
                isESCkey: true,
                isCloseBack: true,
                isKeepOpen: true,
                isBtnClose: true,
                isBtnCallback: true,
                width: '320px',
                title: param.create.title,
                content:
                  '<form name="form_create">'+ fields +
                  '  <ul class="formbox">'+
                  '    <li class="form col0">'+
                  '      <input type="text" name="name" placeholder="" value="">'+
                  '      <label>'+ param.create.label +'</label>'+
                  '    </li>'+
                  '  </ul>'+
                  '</form>',
                eventManagers: {
                  'callback': ['', 'create']
                },
                eventManagerFunctions: function(){
                  event.preventDefault()
                  ajax( {
                    url: '/modal/'+ datable.dataset.datatable +'create',
                    body: new FormData( document.forms.namedItem( 'form_create' )),
                    done: ( back ) => {
                      let response = JSON.parse( back.response )
                      if( response.ok )
                        location.replace( '/'+ ( response.link ? response.link : datable.dataset.datatable ))
                      else
                        dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
                    },
                    fail: ( err ) => {
                      dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                    }
                  } )
                }
              } )
            }
          else if( param?.eventManagers?.add )
            param.eventManagerFunctions( param.eventManagers.add )
        break
        case 'td.icons>i[data-ifunction]':
          let ifunction = eventElement.dataset.ifunction.split( '|' ),
              id = eventElement.closest( 'tr[data-id]' ).dataset.id
          switch( ifunction[1] ){
            case 'modal':
              dialog( {
                type: 'modal',
                id: datable.dataset.datatable +'-'+ ifunction[0],
                isCloseBack: true,
                isKeepOpen: true,
                loadFile: '/modal/'+ datable.dataset.datatable +'/'+ ifunction[0] +'/'+ id,
                eventManagers: {
                  'callback': ['', datable.dataset.datatable + '_' + ifunction[0] + '_callback', id],
                  'close': ['', datable.dataset.datatable + '_' + ifunction[0] + '_close'],
                  'afterBuilding': ['', datable.dataset.datatable + '_' + ifunction[0], id]
                },
                eventManagerFunctions: param.eventManagerFunctions,
                events: param.modalEvents ?? null
              } )
            break
            case 'event':
              param.eventManagerFunctions( ['', datable.dataset.datatable + '_' + ifunction[0], id || null] )
            break
          }
        break
        case 'th[data-order]':
          order = eventElement.dataset.order + ' ' + ( eventElement.classList.contains( 'asc' ) ? 'desc' : 'asc' )
        activePage = 1
          $( 'thead[data-order]', datable ).dataset.order = order
          $$( 'th[data-order]', datable ).forEach( selectOrder => {
            selectOrder.classList.remove('desc')
            selectOrder.classList.remove('asc')
          } )
          eventElement.classList.add( order.includes( ' desc' ) ? 'desc' : 'asc' )
          renderTbody( datable )
        break
        case '.pagination > ul:last-child > li:not(:where(.next, .prev, .active, .limit))':
          activePage = parseInt( $( 'a', eventElement).innerHTML )
          renderTbody( datable )
        break
        case '.pagination .prev':
          activePage -= 1
          renderTbody( datable )
        break
        case '.pagination .next':
        activePage += 1
        renderTbody( datable )
      break
        case '.pagination select[name="limit"]':
          limit = parseInt( eventElement.value )
          activePage = 1
          renderTbody( datable )
        break
        case '.filter-search':
          where.filterSearch = eventElement.value
          datable.dataset.where = JSON.stringify( where )
          activePage = 1
          renderTbody( datable )
        break
        case '.filter-select':
            where.filterSelects = {}
            $$( '.filter-select', datable ).forEach( filterSelect => {
              where.filterSelects[ filterSelect.dataset.field ] = filterSelect.value
            } )
            datable.dataset.where = JSON.stringify( where )
            activePage = 1
            renderTbody( datable )
          break
      }
    
    function getEventFunction(){
      let selector = [
        '.btn.create',
        '.btn.import',
        'td.icons>i[data-ifunction]',
        'th[data-order]',
        '.pagination > ul:last-child > li:not(:where(.next, .prev, .active, .limit))',
        '.pagination .prev',
        '.pagination .next',
        '.pagination select[name="limit"]',
        '.filter-search',
        '.filter-select'
      ].find( (selector) => event.target.closest( selector ) )
      selector ? eventElement = event.target.closest( selector ) : null
      return selector
    }

    function renderTbody( datable ){
      event.preventDefault()
      apiHTML2render( {
        url: '/api/datatable/v1/render_tbody',
        html: {
          'tbody': $( 'tbody', datable ),
          'pagination': $( '.pagination', datable )
        },
        body: new URLSearchParams( {
          datatable: datable.dataset.datatable,
          activePage,
          limit,
          order,
          where
        } )
      } )
    }
  }
}

export function insertRow( datable, row_id ){
  const tbody = $( 'tbody', datable )
  let tr = document.createElement( 'tr' )
  tbody.prepend( tr )
  tr.setAttribute( 'data-id', row_id )

  if( tbody ){
    apiHTML2render( {
      url: './api/datatable/v1/insert_tr',
      html: { 'tr': tr },
      body: new URLSearchParams( {
        datatable: datable.dataset.datatable,
        id: row_id
      } )
    } )
  }
}

export function renderTbody( datable ){
  const
    activePage = $( '.pagination .active', datable ) ? parseInt( $( '.pagination .active', datable ).innerHTML ) : 1,
    limit = $( 'select[name="limit"]', datable ) ? parseInt( $( 'select[name="limit"]', datable ).value ) : 10,
    order = $( 'thead[data-order]', datable ) ? $( 'thead[data-order]', datable ).dataset.order : null,
    where = JSON.parse( datable.dataset.where || '{}' )
  apiHTML2render( {
    url: './api/datatable/v1/render_tbody',
    html: {
      'tbody': $( 'tbody', datable ),
      'pagination': $( '.pagination', datable )
    },
    body: new URLSearchParams( {
      datatable: datable.dataset.datatable,
      activePage,
      limit,
      order,
      where
    } )
  } )
}