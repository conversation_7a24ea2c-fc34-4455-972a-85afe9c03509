@charset "UTF-8";

[data-upload]{
  position: relative;
  &>li:first-child{
    /*margin-top: 1rem;*/
    font-size: 1.25rem;
    background-color: var(--form-focus-border-color);
    position: relative;
    padding: 1rem;
    text-align: center; 
    &>span{ display: none }
    &.has-advanced-upload{
      outline: 2px dashed var(--form-bg-color);
      outline-offset: -10px;
      transition: outline-offset .15s ease-in-out, background-color .15s linear;
      &>label>span{ display: inline }
      &>span{
        width: 100%;
        display: block;
        font-size: 2em;
      }
    }
    &.is-dragover{
      outline-offset: -20px;
      outline-color: var(--form-border-color);
      background-color: var(--form-bg-color);
    }
    &.is-uploading .box__input,
    &.is-success .box__input,
    &.is-error .box__input{ visibility: hidden }
    & .box__success{ animation: appear-from-inside .25s ease-in-out }
    &>input{
      width: 0.1px;
      height: 0.1px;
      opacity: 0;
      overflow: hidden;
      position: absolute;
      z-index: -1;
      & + label{
        max-width: 90%;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
        display: inline-block;
        overflow: hidden;
        &:hover strong{ color: var(--form-bg-color) }
        &>span{ display: none }
      }
      &:focus + label,
      &.has-focus + label{
        outline: 1px dotted var(--form-color);
        outline: -webkit-focus-ring-color auto 5px;
        & strong{ color: var(--form-bg-color) }
      }
    }
  }
  & .box__uploading{ font-style: italic	}
  & .box__uploading,
  & .box__success,
  & .box__error{ display: none }
  & .box__restart{ font-weight: 700	}
  & .box__restart:focus,
  & .box__restart:hover{ color: var(--form-color) }
  &.is-uploading .box__uploading,
  &.is-success .box__success,
  &.is-error .box__error{
    display: block;
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    transform: translateY( -50% );
    background-color: var(--dashboard-bg-color);
    padding: 1rem;
    text-align: center;
  }
  @keyframes appear-from-inside{
    from	{ transform: translateY( -50% ) scale( 0 ); }
    75%		{ transform: translateY( -50% ) scale( 1.1 ); }
    to		{ transform: translateY( -50% ) scale( 1 ); }
  }
}