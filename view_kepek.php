        <form name="form_kepek" method="post" action="kepek" enctype="multipart/form-data">
          <?= ( $_POST['ok'] )? '<p>'.$_POST['ok'].'</p><br>' : '' ?>
          <input type="hidden" name="log" value="in">
          <p>
            Főoldalon lapozható képek sorszáma:
            <input type="text" name="slides" value="<?= implode( ',', $vw['slides'] ) ?>">
            <button name="btn_slides"><PERSON><PERSON><PERSON></button>
          </p>
          <h3>Galériák</h3>
          <p>
            <input type="text" name="newgallery" placeholder="Galéria neve">
            <button name="btn_newgallery">Új galéria</button>
          </p>
          <?php foreach(  $vw['galleries'] as $gallery ){ ?>
          <dl>
            <dt><b><?= $gallery->name ?></b> ké<PERSON><PERSON> so<PERSON>:</dt>
            <dd>
              <input type="text" name="gallery_<?= $gallery->id ?>" value="<?= implode( ',', $gallery->images ) ?>">
              <button name="btn_gallery">Mentés</button> <a class="button" href="/kepek/gdel/<?= $gallery->id ?>">Galéria törlése</a>
            </dd>
          </dl>
          <?php } ?>
          <br><br>
          <p>Kép feltöltése jpg formátumban.</p>
          <p>
            <input type="file" name="image" accept="image/jpg,jpeg">
            <button name="btn_image">Feltöltés</button>
          </p>
          <br>
          <div>
            <h3>Képek</h3>
            <?php foreach( $vw['images'] as $kep ){ ?>
            <div>
              <img src="/<?= $kep ?>" alt="" width="120px">
              <span><?= substr( $kep, 9, -4 ) ?> <a class="button" href="/kepek/kdel/<?= substr( $kep, 9, -4 ) ?>">Kép törlése</a></span>
            </div>
            <?php } ?>
          </div>
        </form>