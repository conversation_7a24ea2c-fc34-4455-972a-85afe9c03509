<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slider Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .slider-container {
            position: relative;
            width: 100%;
            height: 400px;
            overflow: hidden;
            border: 2px solid #ccc;
        }
        
        .slider-container input[type="radio"] {
            display: none;
        }
        
        .slider-wrapper {
            display: flex;
            width: 400%;
            height: 100%;
            animation: autoSlide 12s infinite;
        }
        
        .slide {
            width: 25%;
            height: 100%;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }
        
        .slide:nth-child(1) { background-color: #ff6b6b; color: white; }
        .slide:nth-child(2) { background-color: #4ecdc4; color: white; }
        .slide:nth-child(3) { background-color: #45b7d1; color: white; }
        .slide:nth-child(4) { background-color: #96ceb4; color: white; }
        
        @keyframes autoSlide {
            0%, 22% { transform: translateX(0%); }
            25%, 47% { transform: translateX(-25%); }
            50%, 72% { transform: translateX(-50%); }
            75%, 97% { transform: translateX(-75%); }
            100% { transform: translateX(0%); }
        }
        
        .slider-nav {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }
        
        .slider-nav label {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            border: 2px solid rgba(255, 255, 255, 0.8);
        }
        
        .slider-nav label:hover {
            background-color: rgba(255, 255, 255, 0.8);
        }
        
        /* Manual navigation */
        #slide1:checked ~ .slider-wrapper {
            transform: translateX(0%) !important;
            animation: none !important;
        }
        
        #slide2:checked ~ .slider-wrapper {
            transform: translateX(-25%) !important;
            animation: none !important;
        }
        
        #slide3:checked ~ .slider-wrapper {
            transform: translateX(-50%) !important;
            animation: none !important;
        }
        
        #slide4:checked ~ .slider-wrapper {
            transform: translateX(-75%) !important;
            animation: none !important;
        }
        
        #slide1:checked ~ .slider-nav label:nth-child(1),
        #slide2:checked ~ .slider-nav label:nth-child(2),
        #slide3:checked ~ .slider-nav label:nth-child(3),
        #slide4:checked ~ .slider-nav label:nth-child(4) {
            background-color: #aa825d;
            border-color: #aa825d;
        }
    </style>
</head>
<body>
    <h1>Automatikus Slider Teszt</h1>
    <p>Ez a slider automatikusan 3 másodpercenként vált. Kattints a pontokra a manuális vezérléshez!</p>
    
    <div class="slider-container">
        <input type="radio" name="slider" id="slide1">
        <input type="radio" name="slider" id="slide2">
        <input type="radio" name="slider" id="slide3">
        <input type="radio" name="slider" id="slide4">
        
        <div class="slider-wrapper">
            <div class="slide">1. SLIDE</div>
            <div class="slide">2. SLIDE</div>
            <div class="slide">3. SLIDE</div>
            <div class="slide">4. SLIDE</div>
        </div>
        
        <div class="slider-nav">
            <label for="slide1"></label>
            <label for="slide2"></label>
            <label for="slide3"></label>
            <label for="slide4"></label>
        </div>
    </div>
</body>
</html>
