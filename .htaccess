RewriteEngine on
RewriteBase /

# www leszedés
RewriteCond %{HTTP_HOST} ^www.(.+)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# http -> https
#RewriteCond %{HTTPS} !=on
#RewriteCond %{HTTP_HOST} ^tolgyfavendeghazbalatonszepezd\.hu [NC]
#RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# csak / vége lehet
#RewriteCond %{REQUEST_FILENAME} !-f
#RewriteCond %{REQUEST_URI} !(.[a-zA-Z0-9]{1,5}|/|#(.*))$
#RewriteRule ^(.*)$ /$1/ [R=301,L]

#ha nincs missing.html
RewriteCond %{REQUEST_FILENAME} !missing.html [NC]
#ha nincs favicon
RewriteCond %{REQUEST_FILENAME} !favicon.ico [NC]
#ha nemlétező file
RewriteCond %{REQUEST_FILENAME} !-f
#ha nemlétező kőnyvtár
RewriteCond %{REQUEST_FILENAME} !-d
#akkor legyen átirányitás
RewriteRule ^(.*)$ index.php [QSA]

<ifModule mod_deflate.c>
 AddOutputFilterByType DEFLATE text/plain
 AddOutputFilterByType DEFLATE text/html
 AddOutputFilterByType DEFLATE text/css
 AddOutputFilterByType DEFLATE text/javascript
 AddOutputFilterByType DEFLATE application/javascript
 AddOutputFilterByType DEFLATE application/x-javascript
 AddOutputFilterByType DEFLATE application/x-httpd-php
  
 BrowserMatch ^Mozilla/4 gzip-only-text/html
 BrowserMatch ^Mozilla/4\.0[678] no-gzip
 BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
</ifModule>

<ifModule mod_expires.c>
 #ExpiresDefault "access plus 1 year"
 ExpiresByType image/png "access plus 2 mounths"
 #ExpiresByType image/jpg "access plus 1 mounths"
 #ExpiresByType text/css "access plus 1 hour"
</ifModule>