import { $, $$, getNextDate, getFormatDate, getDayOfYear, getDiffDay } from '../default.js'
import { ajax } from '../asynccom/asynccom.js'

export function loader( icon = null, speed = '1.5s' ){
  let loader = document.createElement( 'div' )
  loader.setAttribute( 'data-loader', '' )
  if( icon ) loader.setAttribute('style', '--icon:var(--icon-' + icon + ')' )
  else loader.innerHTML = '/'
  if( speed ) loader.style.animationDuration = speed
  return loader
}

export function datePicker(){
  const
    pickers = $$( '[data-datepicker]' ),
    months = ['Janu<PERSON>r', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>usz<PERSON>', 'Szeptember', 'Október', 'November', 'December'],
    daysOfWeek = '<li class="days">H</li><li class="days">K</li><li class="days">Sze</li><li class="days">Cs</li><li class="days">P</li><li class="days holiday">Szo</li><li class="days holiday">V</li>'
  
  pickers ? pickers.forEach( picker => {
    const
      param = JSON.parse( picker.dataset.datepicker ),
      ui = document.createElement( 'div' )
    let
      startInput = $( 'input#startinput', picker ),
      stopInput = $( 'input#stopinput', picker ),
      daysInput = $( 'input#daysinput', picker ),
      button = $( 'button', picker ),
      firstMonthFirstDay = startInput.value != '' ? new Date( startInput.value ) : new Date(),
      firstMonthLastDay = new Date( firstMonthFirstDay.getFullYear(), firstMonthFirstDay.getMonth() + 1, 0 ),
      lastMonthFirstDay = firstMonthFirstDay.getMonth() == 11 ? new Date( firstMonthFirstDay.getFullYear() + 1, 0, 1 ) : new Date( firstMonthFirstDay.getFullYear(), firstMonthFirstDay.getMonth() + 1, 1 ),
      lastMonthLastDay = new Date( lastMonthFirstDay.getFullYear(), lastMonthFirstDay.getMonth() + 1, 0 )

    firstMonthFirstDay = new Date( firstMonthFirstDay.getFullYear(), firstMonthFirstDay.getMonth(), 1 )
    if( param.today ) param.minMonth = getFormatDate().substring( 0, 7 )
    else if( !param.minMonth ) param.minMonth = firstMonthFirstDay.getFullYear() + '-01'
    if( !param.maxMonth ) param.maxMonth = firstMonthFirstDay.getFullYear() + '-12'
    if( !param.minPeriodDay ) param.minPeriodDay = 1
    button.disabled = stopInput.value != '' ? false : true

    ui.append( createCalendar( firstMonthFirstDay, firstMonthLastDay, ['next'] ) )
    ui.append( createCalendar( lastMonthFirstDay, lastMonthLastDay, ['next'] ) )

    picker.append( ui )

    if( startInput.value != '' )
      selectedPeriod( startInput.value, stopInput.value ?? '', ui )

    function createCalendar( firstDay, lastDay, paging = false ){
      let
        div = document.createElement( 'div' ),
        header = document.createElement( 'header' ),
        ul = document.createElement( 'ul' ),
        html = '',
        dateString,
        holiday,
        dataDay,
        next = '',
        prev = '',
        reserved = '',
        reservedDay = false,
        reservedStart = getDiffDay( firstDay, param.minMonth + '-01' ) + 1,
        blank = firstDay.getDay() ? firstDay.getDay() - 1 : 6
      if( paging ){
        prev = paging.includes( 'prev' ) ? '<span class="prev"><</span>' : '<b></b>'
        next = paging.includes( 'next' ) ? '<span class="next">></span>' : '<b></b>'
      }
      header.innerHTML = prev + firstDay.getFullYear() +'. '+ months[firstDay.getMonth()] + next
 
      html += daysOfWeek
      for( let i = 0; i < blank; i++ )
        html += '<li class="blank"></li>'

      for( let i = 1; i <= lastDay.getDate(); i++ ){
        dateString = getFormatDate( 'Y-m-d', new Date( firstDay.getFullYear(), firstDay.getMonth(), i ) )
        holiday = new Date( dateString ).getDay() == 0 || new Date( dateString ).getDay() == 6 ? ' class="holiday"' : ''
        dataDay = !param.today || dateString >= getFormatDate() ? ' data-day="'+ dateString +'"' : '*'
        if( param.reserved && param.reserved[reservedStart + i - 1] && param.reserved[reservedStart + i - 1] > 1 ){
          if( !reservedDay )
            holiday = holiday == '' ? ' class="reservedstart"' : ' class="reservedstart holiday"'
          else{
            holiday = holiday == '' ? ' class="reserved"' : ' class="reserved holiday"'
            dataDay = ''
          }
          reservedDay = true
        }else if( reservedDay ){
          holiday = holiday == '' ? ' class="reservedstop"' : ' class="reservedstop holiday"'
          reservedDay = false
        }
        
        html += '<li'+ ( dataDay != '*' ? dataDay : '' ) + holiday +'>'
        html += dataDay == '*' ? '<del>' + i + '</del>' : i
        html += '</li>'
      }
      
      ul.innerHTML = html
      div.append( header )
      div.append( ul )

      return div
    }
    function selectedPeriod( startInput, stopInput ){
      let
        start = $( 'li[data-day="'+ startInput +'"]', ui ),
        stop = $( 'li[data-day="'+ stopInput +'"]', ui ),
        selected = getNextDate( new Date( startInput ) )

      start ? start.classList.add( 'start' ) : null
      if( stopInput != '' ){
        stop ? stop.classList.add( 'stop' ) : null
        while( selected < new Date( stopInput ) ){
          let selectedDay = $( 'li[data-day="'+ getFormatDate( 'Y-m-d', selected ) +'"]', ui )
          selectedDay ? selectedDay.classList.add( 'selected' ) : null
          selected = getNextDate( selected )
        }
      }
    }

    picker.addEventListener( 'click', event => {
      if( event.target.closest( 'li[data-day]' ) ){
        const
          selectedDay = event.target.dataset.day,
          start = $( 'li.start[data-day="'+ startInput.value +'"]', ui ),
          stop = $( 'li.stop[data-day="'+ stopInput.value +'"]', ui ),
          selecteds = $$( 'li.selected[data-day]', ui )
        let selectetReservedId = getDayOfYear( new Date( selectedDay ) ) - getDayOfYear( new Date( param.minMonth + '-01' ) ) + 1
        if( startInput.value == '' ){
          if( !param.reserved || !param.reserved[selectetReservedId - 1] || param.reserved[selectetReservedId - 1] === 3 || param.reserved[selectetReservedId - 1] === 0 ){
            startInput.value = selectedDay
            event.target.classList.add('start' )
          }
        }else if( stopInput.value != '' ){
          if( !param.reserved || !param.reserved[selectetReservedId - 1] || param.reserved[selectetReservedId - 1] === 3 || param.reserved[selectetReservedId - 1] === 0 ){
            start ? start.classList.remove( 'start' ) : null
            stop ? stop.classList.remove( 'stop' ) : null
            selecteds ? selecteds.forEach( selected => { selected.classList.remove( 'selected' ) } ) : null
            stopInput.value = ''
            daysInput ? daysInput.value = '' : null
            button.disabled = true
            startInput.value = selectedDay
            event.target.classList.add('start' )
          }
        }else if( event.target.dataset.day <= startInput.value ){
          if( !param.reserved || !param.reserved[selectetReservedId - 1] || param.reserved[selectetReservedId - 1] === 3 || param.reserved[selectetReservedId - 1] === 0 ){
            start ? start.classList.remove( 'start' ) : null
            startInput.value = selectedDay
            event.target.classList.add( 'start' )
          }
        }else{
          if( !param.reserved || !param.reserved[selectetReservedId - 1] || param.reserved[selectetReservedId - 1] === 1 || param.reserved[selectetReservedId - 1] === 0 ){
            let
              diff = getDiffDay( new Date( selectedDay ), new Date( startInput.value ) ),
              startReservedId = getDayOfYear( new Date( startInput.value ) ) - getDayOfYear( new Date( param.minMonth + '-01' ) ) + 2,
              notReserved = true
            if( diff >= param.minPeriodDay ){
              for( let i = startReservedId; i <= startReservedId + diff - 1; i++ )
                if( param.reserved[i - 1] && param.reserved[i - 1] > 1 ) notReserved = false
              if( notReserved ){
                stopInput.value = selectedDay
                daysInput ? daysInput.value = diff : null
                button.disabled = false
                event.target.classList.add( 'stop' )
                let selected = getNextDate( new Date( startInput.value ) )
                while( selected < new Date( stopInput.value ) ){
                  let selectDay = $( 'li[data-day="'+ getFormatDate( 'Y-m-d', selected ) +'"]', ui )
                  selectDay ? selectDay.classList.add( 'selected' ) : null
                  selected = getNextDate( selected )
                }
              }
            }
          }
        }
      }

      if( event.target.closest( 'header > span.prev' ) ){
        if( param.minMonth < getFormatDate( 'Y-m-d', firstMonthFirstDay ).substring( 0, 7 ) ){
          lastMonthFirstDay = firstMonthFirstDay
          lastMonthLastDay = firstMonthLastDay

          firstMonthFirstDay = lastMonthFirstDay.getMonth() == 0 ? new Date( lastMonthFirstDay.getFullYear() - 1, 11, 1 ) : new Date( lastMonthFirstDay.getFullYear(), lastMonthFirstDay.getMonth() - 1, 1 )
          firstMonthLastDay = new Date( firstMonthFirstDay.getFullYear(), firstMonthFirstDay.getMonth() + 1, 0 )
          ui.innerHTML = ''
          ui.append( createCalendar( firstMonthFirstDay, firstMonthLastDay, ['prev', 'next'] ) )
          ui.append( createCalendar( lastMonthFirstDay, lastMonthLastDay, ['prev', 'next'] ) )
          if( startInput.value != '' )
            selectedPeriod( startInput.value, stopInput.value ?? '', ui )
        }
      }

      if( event.target.closest( 'header > span.next' ) ){
        if( param.maxMonth > getFormatDate( 'Y-m-d', lastMonthFirstDay ).substring( 0, 7 ) ){
          firstMonthFirstDay = lastMonthFirstDay
          firstMonthLastDay = lastMonthLastDay
          lastMonthFirstDay = firstMonthFirstDay.getMonth() == 11 ? new Date( firstMonthFirstDay.getFullYear() + 1, 0, 1 ) : new Date( firstMonthFirstDay.getFullYear(), firstMonthFirstDay.getMonth() + 1, 1 )
          lastMonthLastDay = new Date( lastMonthFirstDay.getFullYear(), lastMonthFirstDay.getMonth() + 1, 0 )
          ui.innerHTML = ''
          ui.append( createCalendar( firstMonthFirstDay, firstMonthLastDay, ['prev', 'next'] ) )
          ui.append( createCalendar( lastMonthFirstDay, lastMonthLastDay, ['prev', 'next'] ) )
          if( startInput.value != '' )
            selectedPeriod( startInput.value, stopInput.value ?? '', ui )
        }
      }
    } )
  } ) : null
}

export function gallery(){
  const
    galleries = $$( '[data-gallery]' )
  
  galleries ? galleries.forEach( gallery => {
    const
      param = JSON.parse( gallery.dataset.gallery ),
      images = $$( 'img', gallery ),
      popover = document.createElement( 'div' ),
      next = document.createElement( 'div' ),
      prev = document.createElement( 'div' ),
      img = document.createElement( 'img' )
    let index = 0
    
    next.classList.add( 'next' )
    next.innerHTML = '<b class="close">x</b><b>></b>'
    prev.classList.add( 'prev' )
    prev.innerHTML = '<b><</b>'
    popover.append( img )
    popover.append( prev )
    popover.append( next )
    popover.popover = 'auto'
    gallery.append( popover )

    gallery.addEventListener( 'click', event => {
      if( event.target.closest( 'img:not([popover] img)' ) ){
        const src = event.target.src
        for( let i = 0; i < images.length; i++ )
          if( images[i].src === src ) index = i
        $( 'img', popover ).src = images[index].src
        popover.showPopover()
      }

      if( event.target.closest( '[popover] b.close' ) )
        popover.hidePopover()
      
      if( event.target.closest( '[popover] > div.prev' ) ){
        index--
        if( index < 0 ) index = images.length - 1
        $( 'img', popover ).src = images[index].src
      }

      if( event.target.closest( '[popover] > div.next' ) ){
        index++
        if( index >= images.length ) index = 0
        $( 'img', popover ).src = images[index].src
      }
    } )
  } ) : null
}

export function library(){
  const
    libraries = $$( '[data-library]' )
  
  libraries ? libraries.forEach( library => {
    let 
      dragSrcEl,
      sequence = JSON.parse( $( 'input[name="sequence"]', library ).value )

    function handleDragStart( event ){
      let e = event.target.closest( 'div[draggable]' )
      if( !e ) return
      e.style.opacity = '0.4'
      dragSrcEl = e
      event.dataTransfer.dropEffect = 'move'
      event.dataTransfer.effectAllowed = 'move'
      event.dataTransfer.setData( 'text/html', e.innerHTML )
      event.dataTransfer.setData( 'text/plain', e.dataset.key )
    }

    function handleDragEnd( event ){
      let e = event.target.closest( 'div[draggable]' )
      if( !e ) return
      e.style.opacity = '1'
      items.forEach( function( item ){
        item.classList.remove( 'over' )
      } )
    }

    function handleDragOver( event ){
      event.preventDefault()
      return false
    }

    function handleDragEnter( event ){
      event.target.closest( 'div[draggable]' ).classList.add( 'over' )
    }

    function handleDragLeave( event ){
      event.target.closest( 'div[draggable]' ).classList.remove( 'over' )
    }

    function handleDrop( event ){
      event.stopPropagation()
      let e = event.target.closest( 'div[draggable]' )
      if( !e ) return false
      if( dragSrcEl !== e ){
        dragSrcEl.innerHTML = e.innerHTML
        dragSrcEl.dataset.key = e.dataset.key

        e.innerHTML = event.dataTransfer.getData( 'text/html' )
        e.dataset.key = event.dataTransfer.getData( 'text/plain' )
        
        dragSrcEl.style.opacity = '1'
        items.forEach( function( item ){
          item.classList.remove( 'over' )
        } )

        let from, to
        for( let i = 0; i < sequence.length; i++ ){
          if( sequence[i] == dragSrcEl.dataset.key ) to = i
          if( sequence[i] == event.dataTransfer.getData( 'text/plain' ) ) from = i
        }
        sequence[to] = parseInt( event.dataTransfer.getData( 'text/plain' ) )
        sequence[from] = parseInt( dragSrcEl.dataset.key )
        $( 'input[name="sequence"]', library ).value = JSON.stringify( sequence )
      }
      return false
    }

    let items = $$( 'div', library )
    items ? items.forEach( function( item ){
      item.addEventListener( 'dragstart', handleDragStart ) // húzás kezdete
      item.addEventListener( 'dragover', handleDragOver )   // eldobási terület fölött van
      $( 'img', item ).addEventListener( 'dragenter', handleDragEnter ) // eldobási területre érkezik
      $( 'img', item ).addEventListener( 'dragleave', handleDragLeave ) // eldobási területről távozik
      item.addEventListener( 'dragend', handleDragEnd )     // húzás vége
      item.addEventListener( 'drop', handleDrop )           // eldobás
      $( 'a', item ).addEventListener( 'click', (event) => {
        event.preventDefault()
        for( let i = 0; i < sequence.length; i++ )
          if( sequence[i] == event.target.closest( 'div[draggable]' ).dataset.key ){
            sequence.splice( i, 1 )
            $( 'input[name="sequence"]', library ).value = JSON.stringify( sequence )
          }
        event.target.closest( 'div[draggable]' ).remove()
      } )
    } ) : null
  } ) : null
}

export function upload(){
  const uploads = $$( '[data-upload]' ) 
  uploads ? uploads.forEach( upload => {
    let
      updb = 0,
      upFiles = null
    
    const
      param = JSON.parse( upload.dataset.upload ),
      input = $( '#foto', upload ),
      fotosor  = ( param.maxdb > 1 )? $( 'input[name="sequence"]' ) : null,
      label	= input.nextSibling,
      errorMsg = $( '.box__error span', upload ),
      restart = $( '.box__restart', upload ),
      showFiles = function( fname ){
        label.textContent = updb > 1 ? ( input.getAttribute( 'data-multiple-caption' ) || '' ).replace( '{count}', updb ) : fname
      },
      isAdvancedUpload = () => { // drag & drop, formdata, filereader elérhető?
        let div = document.createElement( 'div' )
        return ( ( 'draggable' in div ) || ( 'ondragstart' in div && 'ondrop' in div ) ) && 'FormData' in window && 'FileReader' in window
      },
      funUpload = function(){
        if( upload.classList.contains( 'is-uploading' ) ) return false
        upload.classList.add( 'is-uploading' )
        upload.classList.remove( 'is-error' )
      
        if( upFiles ){
          let
            tomb   = [],
            kid    = 1,
            maxkid = 0,
            upFileIds = []

          updb = ( upFiles.length > param.maxdb )? param.maxdb : upFiles.length
          showFiles( upFiles[0].name )
          
          for( const [key, file] of Object.entries( upFiles ) ){
            let
              div = document.createElement( 'div' ),
              fd = new FormData()

            div.innerHTML = '<img src="/shared/js/spritzer/ui/loader.gif">'
            if( param.imageId )
              $( 'li[data-library]>div' ).innerHTML = div.innerHTML
            else
              $( 'li[data-library]' ).appendChild( div )
            upload.classList.remove( 'is-uploading' )
            fd.append( 'myFile', file )
            // sequence módosításához a szükséges adatok, ha kell
            if( param.tableName ) fd.append( 'tablename', param.tableName )
            if( param.tableId ) fd.append( 'id', param.tableId )
            if( param.imageId ) fd.append( 'imageid', param.imageId )
            ajax( {
              url: '/modal/uploadfile',
              body: fd,
              done: ( back ) => {
                let response = JSON.parse( back.response )
                if( response.ok ){
                  if( param.maxdb > 1 ){
                    $( 'img', div ).src=response.src
                    div.setAttribute( 'draggable', true )
                    div.dataset.key = response.id
                    div.innerHTML = '<a style="--icon:var(--icon-trash-empty)" title = "Törlés" onclick = "return confirm( \'Biztos törlöd?\' )"></a>' + div.innerHTML
                  }else
                    $( 'li[data-library]>div>img' ).src=response.src
                  updb--
                  param.maxdb--
                  if( updb ){
                    showFiles( '' )
                    $( '.box__icon', upload ).textContent = 'max ' + param.maxdb + ' db'
                  }else
                    label.textContent = ''
                  upload.classList.add( response.ok == true ? 'is-success' : 'is-error' )
                  if( !response.success ) errorMsg.textContent = response.error
                }
              }
            } )  
          }
        }
      }

    input.addEventListener( 'change', function( event ){
      upFiles = event.target.files
      funUpload()
    } )
      
    restart.addEventListener( 'click', function( event ){
      event.preventDefault()
      upload.classList.remove( 'is-error', 'is-success' )
      input.click()
    } )

    if( isAdvancedUpload ){
      $( 'li:first-child', upload ).classList.add( 'has-advanced-upload' )

      upload.addEventListener( 'drag', function( event ){
        event.preventDefault()
        event.stopPropagation()  
      } )

      upload.addEventListener( 'dragstart', function( event ){
        event.preventDefault()
        event.stopPropagation()  
      } )

      upload.addEventListener( 'dragend', function( event ){
        event.preventDefault()
        event.stopPropagation()
        upload.classList.remove( 'is-dragover' )
      } )

      upload.addEventListener( 'dragover', function( event ){
        event.preventDefault()
        event.stopPropagation()
        upload.classList.add( 'is-dragover' )
      } )

      upload.addEventListener( 'dragenter', function( event ){
        event.preventDefault()
        event.stopPropagation()
        upload.classList.add( 'is-dragover' )
      } )

      upload.addEventListener( 'dragleave', function( event ){
        event.preventDefault()
        event.stopPropagation()
        upload.classList.remove( 'is-dragover' )
      } )

      upload.addEventListener( 'drop', function( event ){
        event.preventDefault()
        event.stopPropagation()
        upload.classList.remove( 'is-dragover' )
        upFiles = event.dataTransfer.files
        funUpload()
      } )

    }
    
    input.addEventListener( 'focus', function(){ input.classList.add( 'has-focus' ) } )
    input.addEventListener( 'blur', function(){ input.classList.remove( 'has-focus' ) } )

  } ) : null
}

export function toggleButton(){
  const
    buttons = $$( '.toggle-buttons' )
  buttons ? buttons.forEach( button => {
    const
      spans = $$( 'div > span', button ),
      input = $( 'input', button )
    let inputData = input.value.split( '' )
    spans ? spans.forEach( span => {
      span.addEventListener( 'click', function(){
        span.classList.toggle( 'soon' )
        inputData[span.dataset.index] = inputData[span.dataset.index] ? 0 : 1
        input.value = inputData.join( '' )
      } )
    } ) : null
  } ) : null
}
