<!DOCTYPE html>
<html lang="hu">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LELLE Étterem és Pizzéria</title>
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700&family=Poppins:wght@400&display=swap" rel="stylesheet">
  <style>
    body {
      margin: 0;
      font-family: 'Poppins', sans-serif;
      background-color: #fff;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #f8f8f8;
      border-bottom: 1px solid #ddd;
    }

    .logo {
      text-align: left;
      line-height: 1;
    }

    .logo .main {
      font-family: 'Playfair Display', serif;
      font-size: 48px;
      font-weight: 700;
      letter-spacing: 2px;
      text-align: center;
      color: #d62828;
    }

    .logo .sub {
      font-family: '<PERSON>pin<PERSON>', sans-serif;
      font-size: 16px;
      letter-spacing: 3.5px;
      color: #333;
      text-align: center;
      text-transform: uppercase;
    }

    .nav {
      display: flex;
      gap: 12px;
    }

    .nav a {
      text-decoration: none;
      color: #333;
      font-size: 0.95rem;
      padding: 6px 8px;
      border-radius: 5px;
      transition: background 0.3s;
    }

    .nav a:hover {
      background-color: #ffe5e5;
      color: #d62828;
    }

    @media (max-width: 480px) {
      .header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
      .nav {
        justify-content: flex-start;
        flex-wrap: wrap;
      }
    }
  </style>
</head>
<body>

  <header class="header">
    <a href="/lelle" class="logo">
      <div class="main">LELLE</div>
      <div class="sub">Étterem & Pizzéria</div>
    </a>
    <nav class="nav">
      <a href="etlap">Étlap</a>
      <a href="galeria">Galéria</a>
      <a href="terkep">Térkép</a>
    </nav>
  </header>

</body>
</html>
