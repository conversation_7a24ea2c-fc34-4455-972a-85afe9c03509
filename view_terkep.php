        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" loading="lazy">
        <div id="map">
          <h2>Hol találsz minket</h2>
          <div style="text-align: center;margin-bottom: 3rem">
            8638 <PERSON><PERSON>, Szent István tér 5.<br>
            Asztalfoglalás: +36 20 289 3123<br>
            <a href="https://www.facebook.com/profile.php?id=100063937519376" style="--icon:var(--icon-facebook)"></a>
          </div>
          <div id="map-container"></div>
          <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
          <script>
            let
              map = L.map( 'map-container' ).setView( [46.787708, 17.693956], 16 ),
              tiles = <PERSON><PERSON>tile<PERSON>ayer( 'https://tile.openstreetmap.org/{z}/{x}/{y}.png' )
                       .addTo( map ),
              marker = L.marker( [46.787708, 17.693956] )
                        .addTo( map )
                        .bindPopup( '8638 Balatonlelle, Szent István tér 5.' ).openPopup()
          </script>
        </div>
        