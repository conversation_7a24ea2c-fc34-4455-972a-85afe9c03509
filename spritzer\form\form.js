import { $, $$ } from '../default.js'

export function validation( element = '' ){
  const
    forms = $$( element+ ' form:has(.validation)' )
  forms ? forms.forEach( form => {
    const
      validations = $$( '.validation', form ),
      updateValidationStateForInput = ( inputEl ) => {
        const isInputValid = inputEl.checkValidity(),
        errorEl = inputEl?.nextElementSibling?.nextElementSibling
        if( !inputEl.required && inputEl.value === '' && isInputValid )
          inputEl.classList.remove( 'is-valid', 'is-invalid' )
        else{
          inputEl.classList.toggle( 'is-valid', isInputValid )
          inputEl.classList.toggle( 'is-invalid', !isInputValid )
        }
        if( errorEl ){
          $( '.error', errorEl ).innerHTML = inputEl.validationMessage
          $( '.error', errorEl ).hidden = isInputValid
          if( $( '.validation-submit' )) $( '.validation-submit' ).disabled = !isInputValid
        }
      },
      onSubmit = ( event ) => {
        validations.forEach( updateValidationStateForInput )
        const
          formEl = event.target,
          isFormValid = formEl.checkValidity()
        if( !isFormValid ) event.preventDefault()
        $( 'input:invalid', formEl )?.focus()
      }
    
    form.setAttribute( 'novalidate', '' )
    form.addEventListener( 'submit', onSubmit )
    
    validations ? validations.forEach( inputEl => {
      inputEl.addEventListener( 'input', ( event ) => {
        updateValidationStateForInput( event.target )
      } )
      inputEl.addEventListener( 'blur', (event) => {
        updateValidationStateForInput( event.target )
      } )
      if( inputEl.value !== '' )
        updateValidationStateForInput( inputEl )
    } ) : null
  } ) : null
}